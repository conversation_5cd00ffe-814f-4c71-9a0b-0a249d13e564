const axios = require('axios');
const { config } = require('./config');
const logger = require('./logger');

class EncompassApi {
  constructor() {
    this.baseUrl = config.encompass.apiUrl;
    this.accessToken = null;
    this.tokenExpiry = null;
  }

  async authenticate() {
    try {
      logger.info('Authenticating with Encompass API...');

      // Create URL-encoded form data
      const authParams = new URLSearchParams();
      authParams.append('grant_type', 'password');
      authParams.append('username', config.encompass.username);
      authParams.append('password', config.encompass.password);
      authParams.append('client_id', config.encompass.clientId);
      authParams.append('client_secret', config.encompass.clientSecret);

      logger.info('Auth request details:', {
        url: `${this.baseUrl}/oauth2/v1/token`,
        client_id: config.encompass.clientId,
        username: config.encompass.username,
        // Don't log sensitive data, just confirm they exist
        has_password: !!config.encompass.password,
        has_client_secret: !!config.encompass.clientSecret
      });

      const response = await axios.post(`${this.baseUrl}/oauth2/v1/token`, authParams.toString(), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      this.accessToken = response.data.access_token;
      this.tokenExpiry = Date.now() + (response.data.expires_in * 1000);

      logger.info('Successfully authenticated with Encompass API');
      return true;
    } catch (error) {
      logger.error('Failed to authenticate with Encompass API');

      if (error.response) {
        logger.error('Response status:', error.response.status);
        logger.error('Response data:', error.response.data);

        if (error.response.status === 401) {
          logger.error('🔑 Authentication failed - please check:');
          logger.error('   1. Client ID and Client Secret are correct');
          logger.error('   2. Username and Password are correct');
          logger.error('   3. User has API access permissions');
          logger.error('   4. Instance ID is correct (if required)');
        }
      } else {
        logger.error('Request error:', error.message);
      }

      throw error;
    }
  }

  async ensureAuthenticated() {
    if (!this.accessToken || Date.now() >= this.tokenExpiry) {
      await this.authenticate();
    }
  }

  async getRecentLeads(sinceDate = null) {
    try {
      await this.ensureAuthenticated();
      
      // Default to last 24 hours if no date provided
      if (!sinceDate) {
        sinceDate = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
      }

      logger.info(`Fetching leads from Encompass since ${sinceDate}`);

      // Note: This is a simplified example. The actual Encompass API endpoints
      // may vary based on your specific setup and permissions
      const response = await axios.get(`${this.baseUrl}/encompass/v1/loans`, {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json'
        },
        params: {
          filter: `fields(1109,4000,4002,4003,4004,4008,4009,4010,4011,4012,4013,4014,4015,4016,4017,4018,4019,4020,4021,4022,4023,4024,4025,4026,4027,4028,4029,4030,4031,4032,4033,4034,4035,4036,4037,4038,4039,4040,4041,4042,4043,4044,4045,4046,4047,4048,4049,4050,4051,4052,4053,4054,4055,4056,4057,4058,4059,4060,4061,4062,4063,4064,4065,4066,4067,4068,4069,4070,4071,4072,4073,4074,4075,4076,4077,4078,4079,4080,4081,4082,4083,4084,4085,4086,4087,4088,4089,4090,4091,4092,4093,4094,4095,4096,4097,4098,4099,4100)`,
          start: 0,
          limit: 100
        }
      });

      const leads = this.transformEncompassData(response.data);
      logger.info(`Retrieved ${leads.length} leads from Encompass`);
      
      return leads;
    } catch (error) {
      logger.error('Failed to fetch leads from Encompass', error.response?.data || error.message);
      throw error;
    }
  }

  transformEncompassData(rawData) {
    // Transform Encompass loan data to a standardized format
    if (!rawData || !Array.isArray(rawData)) {
      return [];
    }

    return rawData.map(loan => ({
      id: loan.loanNumber || loan.guid,
      firstName: loan['4002'] || '', // Borrower First Name
      lastName: loan['4000'] || '',  // Borrower Last Name
      email: loan['4008'] || '',     // Borrower Email
      phone: loan['4009'] || '',     // Borrower Phone
      address: loan['4010'] || '',   // Property Address
      city: loan['4011'] || '',      // Property City
      state: loan['4012'] || '',     // Property State
      zipCode: loan['4013'] || '',   // Property Zip
      loanAmount: loan['1109'] || 0, // Loan Amount
      loanPurpose: loan['4003'] || '', // Loan Purpose
      source: 'Encompass',
      createdAt: loan.createdDateTime || new Date().toISOString(),
      updatedAt: loan.lastModified || new Date().toISOString()
    }));
  }
}

module.exports = EncompassApi;
