const EncompassApi = require('./encompassApi');
const axios = require('axios');
const logger = require('./logger');

async function testEncompassPermissions() {
  console.log('🔍 Testing Encompass API Permissions...\n');

  try {
    // Initialize Encompass API
    const encompassApi = new EncompassApi();
    
    console.log('1. Testing Encompass API Authentication...');
    
    // Test authentication
    const authResult = await encompassApi.authenticate();
    if (!authResult) {
      throw new Error('Failed to authenticate with Encompass API');
    }
    console.log('✅ Successfully authenticated with Encompass API\n');

    // Test different endpoints to see what we have access to
    const baseUrl = 'https://api.elliemae.com';
    const headers = {
      'Authorization': `Bearer ${encompassApi.accessToken}`,
      'Content-Type': 'application/json'
    };

    console.log('2. Testing various API endpoints for permissions...\n');

    // Test endpoints in order of likelihood to work
    const endpoints = [
      {
        name: 'User Info',
        url: `${baseUrl}/encompass/v1/users/current`,
        description: 'Get current user information'
      },
      {
        name: 'Schema Info',
        url: `${baseUrl}/encompass/v1/schema/loan/fieldDescriptors`,
        description: 'Get loan field schema (usually accessible)'
      },
      {
        name: 'Pipeline View',
        url: `${baseUrl}/encompass/v1/pipeline/views`,
        description: 'Get pipeline views'
      },
      {
        name: 'Loan Count',
        url: `${baseUrl}/encompass/v1/loans`,
        params: { limit: 1 },
        description: 'Get minimal loan data (1 record)'
      },
      {
        name: 'Loan Folders',
        url: `${baseUrl}/encompass/v1/loanfolders`,
        description: 'Get loan folders'
      },
      {
        name: 'Business Contacts',
        url: `${baseUrl}/encompass/v1/businessContacts`,
        params: { limit: 5 },
        description: 'Get business contacts'
      }
    ];

    const results = [];

    for (const endpoint of endpoints) {
      try {
        console.log(`Testing: ${endpoint.name} - ${endpoint.description}`);
        
        const config = {
          method: 'GET',
          url: endpoint.url,
          headers: headers,
          timeout: 10000
        };

        if (endpoint.params) {
          config.params = endpoint.params;
        }

        const response = await axios(config);
        
        console.log(`✅ ${endpoint.name}: SUCCESS (${response.status})`);
        console.log(`   Data type: ${Array.isArray(response.data) ? 'Array' : typeof response.data}`);
        console.log(`   Records: ${Array.isArray(response.data) ? response.data.length : 'N/A'}`);
        
        results.push({
          endpoint: endpoint.name,
          status: 'SUCCESS',
          statusCode: response.status,
          dataType: Array.isArray(response.data) ? 'Array' : typeof response.data,
          recordCount: Array.isArray(response.data) ? response.data.length : null,
          sampleData: JSON.stringify(response.data).substring(0, 200) + '...'
        });

      } catch (error) {
        const status = error.response?.status || 'ERROR';
        const message = error.response?.data?.summary || error.response?.data?.error || error.message;
        
        console.log(`❌ ${endpoint.name}: FAILED (${status}) - ${message}`);
        
        results.push({
          endpoint: endpoint.name,
          status: 'FAILED',
          statusCode: status,
          error: message
        });
      }
      
      console.log(''); // Empty line for readability
    }

    console.log('3. Permission Test Summary:');
    console.log('===========================');
    
    const successful = results.filter(r => r.status === 'SUCCESS');
    const failed = results.filter(r => r.status === 'FAILED');
    
    console.log(`✅ Successful endpoints: ${successful.length}/${results.length}`);
    console.log(`❌ Failed endpoints: ${failed.length}/${results.length}\n`);

    if (successful.length > 0) {
      console.log('📊 Accessible Endpoints:');
      successful.forEach(result => {
        console.log(`   - ${result.endpoint}: ${result.recordCount !== null ? result.recordCount + ' records' : 'Available'}`);
      });
      console.log('');
    }

    if (failed.length > 0) {
      console.log('🚫 Restricted Endpoints:');
      failed.forEach(result => {
        console.log(`   - ${result.endpoint}: ${result.error}`);
      });
      console.log('');
    }

    console.log('4. Recommendations:');
    console.log('==================');
    
    if (successful.some(r => r.endpoint === 'Loan Count' || r.endpoint === 'Loan Folders')) {
      console.log('✅ You have loan access! The integration should work.');
      console.log('   Try adjusting the field filters or date ranges.');
    } else if (successful.some(r => r.endpoint === 'Business Contacts')) {
      console.log('⚠️  You have contact access but not loan access.');
      console.log('   Contact your Encompass administrator to grant loan API permissions.');
    } else if (successful.some(r => r.endpoint === 'User Info')) {
      console.log('⚠️  Basic API access confirmed, but no data access.');
      console.log('   Contact your Encompass administrator to grant data permissions.');
    } else {
      console.log('❌ Limited API access detected.');
      console.log('   Contact your Encompass administrator to review API permissions.');
    }

    console.log('\n📋 Next Steps:');
    if (successful.length > 0) {
      console.log('1. Review accessible endpoints above');
      console.log('2. If loan access is available, modify the integration to use working endpoints');
      console.log('3. If no loan access, contact Encompass admin for permissions');
    } else {
      console.log('1. Contact your Encompass administrator');
      console.log('2. Request API permissions for loan data access');
      console.log('3. Verify the user account has proper roles assigned');
    }

  } catch (error) {
    console.error('❌ Permission test failed:', error.message);
    console.error('\n🔧 Troubleshooting:');
    console.error('1. Verify authentication is working');
    console.error('2. Check network connectivity');
    console.error('3. Contact Encompass support if issues persist');
    process.exit(1);
  }
}

// Run test if this file is executed directly
if (require.main === module) {
  testEncompassPermissions()
    .then(() => {
      console.log('\n✅ Permission test completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Permission test failed:', error);
      process.exit(1);
    });
}

module.exports = { testEncompassPermissions };
