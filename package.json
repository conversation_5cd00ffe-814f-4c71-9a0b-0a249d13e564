{"name": "encompass-gohighlevel-integration", "version": "2.0.0", "description": "Enhanced Encompass to GoHighLevel integration with webhook support and comprehensive dashboard", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "legacy": "node encompass-to-gohighlevel-integration.js", "integration": "node src/integration-service.js", "webhook": "node src/webhook-server.js", "borrower-count": "node src/scripts/get-borrower-count.js", "test-comprehensive": "node src/scripts/test-comprehensive-extraction.js", "setup-webhook": "node src/scripts/setup-webhook.js", "token-info": "node encompass-token-manager.js info", "token-refresh": "node encompass-token-manager.js refresh", "fetch-borrowers": "node fetch-borrowers.js", "test-webhook": "node test-external-user-webhook.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["encompass", "gohighlevel", "crm", "mortgage", "integration", "api"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.6.0", "dotenv": "^16.6.1", "express": "^4.18.2", "node-cron": "^3.0.3", "sqlite3": "^5.1.6"}, "devDependencies": {"nodemon": "^3.0.1"}}