{"name": "encompass-gohighlevel-integration", "version": "1.0.0", "description": "Automated integration between Encompass mortgage software and GoHighLevel CRM", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "webhook-server": "node encompass-webhook-integration.js", "test": "node test-connection.js", "test-integration": "node test-encompass-integration.js", "test-encompass": "node test-encompass-data.js", "test-permissions": "node test-encompass-permissions.js", "test-users": "node test-users-and-leads.js", "test-data": "node test-accessible-data.js", "fetch-all": "node fetch-all-encompass-data.js", "fetch-contacts": "node fetch-borrower-contacts-paginated.js", "test-enhanced": "node test-enhanced-features.js", "sync-once": "node index.js --once", "setup-webhooks": "node setup-encompass-webhooks.js", "webhook-list": "node setup-encompass-webhooks.js list", "webhook-resources": "node setup-encompass-webhooks.js resources", "token-info": "node encompass-token-manager.js info", "token-refresh": "node encompass-token-manager.js refresh", "new-integration": "node src/integration-service.js", "new-webhook": "node src/webhook-server.js", "borrower-count": "node src/scripts/get-borrower-count.js", "test-comprehensive": "node src/scripts/test-comprehensive-extraction.js", "setup-webhook": "node src/scripts/setup-webhook.js"}, "keywords": ["encompass", "gohighlevel", "crm", "mortgage", "integration", "api"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.6.0", "dotenv": "^16.6.1", "express": "^4.18.2", "node-cron": "^3.0.3", "sqlite3": "^5.1.6"}, "devDependencies": {"nodemon": "^3.0.1"}}