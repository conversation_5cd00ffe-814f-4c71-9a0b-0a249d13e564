{"name": "encompass-gohighlevel-integration", "version": "1.0.0", "description": "Automated integration between Encompass mortgage software and GoHighLevel CRM", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "node test-connection.js", "test-encompass": "node test-encompass-data.js", "test-enhanced": "node test-enhanced-features.js", "sync-once": "node index.js --once"}, "keywords": ["encompass", "gohighlevel", "crm", "mortgage", "integration", "api"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.6.0", "dotenv": "^16.6.1", "express": "^4.18.2", "node-cron": "^3.0.3"}, "devDependencies": {"nodemon": "^3.0.1"}}