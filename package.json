{"name": "encompass-gohighlevel-integration", "version": "1.0.0", "description": "Automated integration between Encompass mortgage software and GoHighLevel CRM", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["encompass", "gohighlevel", "crm", "mortgage", "integration", "api"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.6.0", "dotenv": "^16.3.1", "node-cron": "^3.0.3"}, "devDependencies": {"nodemon": "^3.0.1"}}