# Encompass to GoHighLevel Real-Time Webhook Integration

This project provides a real-time integration between Encompass mortgage software and GoHighLevel CRM using webhooks to automatically transfer new borrower contacts as they are created.

## 🚀 Features

- **Real-time Integration**: Webhooks automatically detect new borrower creation in Encompass
- **Token Management**: Automatic token refresh and persistent storage
- **Paginated Data Fetching**: Efficient handling of large datasets (210,000+ contacts)
- **Error Handling**: Comprehensive error handling and retry logic
- **Progress Tracking**: SQLite database for webhook event logging
- **Data Transformation**: Smart mapping between Encompass and GoHighLevel data formats

## 📋 Prerequisites

- Node.js 14.0.0 or higher
- Encompass API credentials with appropriate permissions
- GoHighLevel API key and location ID
- Public webhook endpoint (for production)

## 🛠️ Installation

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Configure Environment Variables**
   
   Copy `.env.example` to `.env` and fill in your credentials:
   ```env
   # Encompass API Configuration
   ENCOMPASS_CLIENT_ID=your_client_id_here
   ENCOMPASS_CLIENT_SECRET=your_client_secret_here
   ENCOMPASS_USERNAME=contractoradmin@encompass:**********
   ENCOMPASS_PASSWORD=your_password_here
   ENCOMPASS_INSTANCE_ID=**********

   # GoHighLevel API Configuration
   GOHIGHLEVEL_API_URL=https://services.leadconnectorhq.com
   GOHIGHLEVEL_API_KEY=your_ghl_api_key_here
   GOHIGHLEVEL_LOCATION_ID=your_ghl_location_id_here

   # Webhook Configuration
   WEBHOOK_PORT=3000
   WEBHOOK_CALLBACK_URL=https://your-domain.com/webhook/encompass
   ENCOMPASS_WEBHOOK_SIGNING_KEY=your-webhook-signing-key-here
   ```

## 🧪 Testing

Run the comprehensive test suite to verify all components:

```bash
npm run test-integration
```

This will test:
- ✅ Token management and authentication
- ✅ Encompass API connectivity
- ✅ Borrower contacts fetching
- ✅ GoHighLevel API connectivity
- ✅ Data transformation

## 📡 Webhook Setup

### 1. Set Up Webhook Subscriptions

```bash
# View available webhook resources
npm run webhook-resources

# Set up webhook subscriptions for borrower contacts
npm run setup-webhooks

# List existing subscriptions
npm run webhook-list
```

### 2. Start Webhook Server

```bash
# Start the webhook server
npm run webhook-server
```

The server will:
- Listen for Encompass webhook events on `/webhook/encompass`
- Automatically process new borrower contact creation
- Transform data and push to GoHighLevel
- Log all events to SQLite database

### 3. Webhook Endpoints

- **Health Check**: `GET /health`
- **Status**: `GET /status`
- **Webhook Receiver**: `POST /webhook/encompass`

## 📊 Data Fetching

### Fetch Borrower Contacts with Pagination

```bash
# Fetch first 5 pages (5,000 contacts)
npm run fetch-contacts -- --max-pages 5

# Fetch with custom page size
npm run fetch-contacts -- --page-size 500 --max-pages 10

# Fetch with detailed contact information
npm run fetch-contacts -- --fetch-details true --max-pages 2

# Start from specific position
npm run fetch-contacts -- --start-from 5000 --max-pages 10
```

### Token Management

```bash
# Check token status
npm run token-info

# Force refresh token
npm run token-refresh
```

## 🔧 Architecture

### Core Components

1. **Token Manager** (`encompass-token-manager.js`)
   - Handles Encompass API authentication
   - Automatic token refresh
   - Persistent token storage

2. **Webhook Integration** (`encompass-webhook-integration.js`)
   - Express.js server for webhook handling
   - Real-time event processing
   - GoHighLevel contact creation

3. **Borrower Contacts Fetcher** (`fetch-borrower-contacts-paginated.js`)
   - Efficient pagination handling
   - Batch processing with rate limiting
   - Progress tracking and resumption

4. **Webhook Setup** (`setup-encompass-webhooks.js`)
   - Webhook subscription management
   - Resource and event discovery

### Data Flow

```
Encompass → Webhook Event → Processing → Data Transformation → GoHighLevel
    ↓              ↓              ↓              ↓              ↓
New Borrower → JSON Payload → Extract Data → Map Fields → Create Contact
```

## 📈 Monitoring

### Database Tables

- **webhook_events**: All webhook events with processing status
- **encompass_tokens**: Token storage and expiration tracking

### Logs and Status

```bash
# Check webhook processing status
curl http://localhost:3000/status

# Health check
curl http://localhost:3000/health
```

## 🔍 Data Mapping

### Encompass → GoHighLevel Field Mapping

| Encompass Field | GoHighLevel Field | Notes |
|----------------|-------------------|-------|
| `firstName` | `firstName` | Direct mapping |
| `lastName` | `lastName` | Direct mapping |
| `personalEmail` | `email` | Primary email |
| `homePhone` | `phone` | Primary phone |
| `currentMailingAddress` | `address1`, `city`, `state`, `postalCode` | Address components |
| `id` | `customFields.encompass_id` | Reference ID |
| `ownerId` | `customFields.encompass_owner_id` | Owner reference |
| `birthdate` | `customFields.birth_date` | Custom field |
| `referral` | `customFields.referral_source` | Custom field |

## 🚨 Troubleshooting

### Common Issues

1. **Authentication Errors**
   ```bash
   # Check token status
   npm run token-info
   
   # Refresh token
   npm run token-refresh
   ```

2. **Webhook Not Receiving Events**
   - Verify webhook URL is publicly accessible
   - Check webhook subscription status
   - Verify signing key configuration

3. **GoHighLevel API Errors**
   - Verify API key and location ID
   - Check rate limits
   - Validate contact data format

### Debug Mode

Set environment variable for detailed logging:
```bash
DEBUG=true npm run webhook-server
```

## 📝 API Documentation

### Encompass APIs Used

- **OAuth2 Token**: `POST /oauth2/v1/token`
- **Borrower Contacts Selector**: `POST /encompass/v1/borrowerContactSelector`
- **Borrower Contact Details**: `GET /encompass/v1/borrowerContacts/{id}`
- **Webhook Subscriptions**: `POST /webhook/v1/subscriptions`

### GoHighLevel APIs Used

- **Create Contact**: `POST /contacts/`
- **Get Contacts**: `GET /contacts/`

## 🔐 Security

- Webhook signature verification using HMAC-SHA256
- Token encryption and secure storage
- Environment variable configuration
- Rate limiting and error handling

## 📊 Performance

- **Pagination**: Handles 210,000+ contacts efficiently
- **Rate Limiting**: 500ms delays between requests
- **Batch Processing**: 1000 contacts per page
- **Memory Efficient**: Streaming data processing

## 🎯 Next Steps

1. **Production Deployment**
   - Deploy webhook server to cloud platform
   - Configure public webhook URL
   - Set up monitoring and alerts

2. **Enhanced Features**
   - Bidirectional sync
   - Custom field mapping
   - Duplicate detection
   - Bulk operations

3. **Monitoring**
   - Dashboard for webhook events
   - Performance metrics
   - Error alerting

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Run the test suite: `npm run test-integration`
3. Review webhook logs and database
4. Verify API credentials and permissions
