const EncompassApi = require('./encompassApi');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

async function fetchAllEncompassData() {
  console.log('🔄 Fetching ALL Accessible Encompass Data...\n');

  try {
    // Initialize Encompass API
    const encompassApi = new EncompassApi();
    
    console.log('1. Authenticating with Encompass API...');
    const authResult = await encompassApi.authenticate();
    if (!authResult) {
      throw new Error('Failed to authenticate with Encompass API');
    }
    console.log('✅ Successfully authenticated\n');

    const baseUrl = 'https://api.elliemae.com';
    const headers = {
      'Authorization': `Bearer ${encompassApi.accessToken}`,
      'Content-Type': 'application/json'
    };

    // Create data directory
    const dataDir = path.join(__dirname, 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    const allData = {
      timestamp: new Date().toISOString(),
      summary: {},
      data: {}
    };

    console.log('2. Fetching ALL Users (100 available)...');
    try {
      const usersResponse = await axios.get(`${baseUrl}/encompass/v1/users`, {
        headers: headers,
        params: { limit: 1000 } // Get all users
      });

      const users = usersResponse.data;
      console.log(`✅ Fetched ${users.length} users`);
      
      allData.data.users = users;
      allData.summary.users = {
        total: users.length,
        active: users.filter(u => u.isActive).length,
        inactive: users.filter(u => !u.isActive).length
      };

      // Save users separately
      fs.writeFileSync(
        path.join(dataDir, 'encompass-all-users.json'),
        JSON.stringify({
          timestamp: new Date().toISOString(),
          total: users.length,
          users: users
        }, null, 2)
      );
      console.log('💾 Users saved to: encompass-all-users.json');

    } catch (error) {
      console.log(`❌ Failed to fetch users: ${error.response?.data?.summary || error.message}`);
      allData.data.users = [];
      allData.summary.users = { error: error.message };
    }

    console.log('\n3. Fetching ALL Business Contacts (725 available)...');
    try {
      let allContacts = [];
      let page = 0;
      const limit = 100;
      let hasMore = true;

      while (hasMore) {
        const contactsResponse = await axios.get(`${baseUrl}/encompass/v1/businessContacts`, {
          headers: headers,
          params: { 
            limit: limit,
            start: page * limit
          }
        });

        const contacts = contactsResponse.data;
        allContacts = allContacts.concat(contacts);
        
        console.log(`   📄 Page ${page + 1}: ${contacts.length} contacts`);
        
        hasMore = contacts.length === limit;
        page++;
        
        // Safety break to avoid infinite loop
        if (page > 20) break;
      }

      console.log(`✅ Fetched ${allContacts.length} total business contacts`);
      
      allData.data.businessContacts = allContacts;
      allData.summary.businessContacts = {
        total: allContacts.length,
        withEmail: allContacts.filter(c => c.personalEmail || c.businessEmail || c.primaryEmail).length,
        withPhone: allContacts.filter(c => c.workPhone || c.homePhone || c.mobilePhone || c.primaryPhone).length
      };

      // Save contacts separately
      fs.writeFileSync(
        path.join(dataDir, 'encompass-all-business-contacts.json'),
        JSON.stringify({
          timestamp: new Date().toISOString(),
          total: allContacts.length,
          contacts: allContacts
        }, null, 2)
      );
      console.log('💾 Business contacts saved to: encompass-all-business-contacts.json');

    } catch (error) {
      console.log(`❌ Failed to fetch business contacts: ${error.response?.data?.summary || error.message}`);
      allData.data.businessContacts = [];
      allData.summary.businessContacts = { error: error.message };
    }

    console.log('\n4. Fetching ALL Loan Folders...');
    try {
      const foldersResponse = await axios.get(`${baseUrl}/encompass/v1/loanfolders`, {
        headers: headers
      });

      const folders = foldersResponse.data;
      console.log(`✅ Fetched ${folders.length} loan folders`);
      
      allData.data.loanFolders = folders;
      allData.summary.loanFolders = {
        total: folders.length,
        regular: folders.filter(f => f.folderType === 'Regular').length,
        archive: folders.filter(f => f.folderType === 'Archive').length
      };

      // Save folders separately
      fs.writeFileSync(
        path.join(dataDir, 'encompass-all-loan-folders.json'),
        JSON.stringify({
          timestamp: new Date().toISOString(),
          total: folders.length,
          folders: folders
        }, null, 2)
      );
      console.log('💾 Loan folders saved to: encompass-all-loan-folders.json');

    } catch (error) {
      console.log(`❌ Failed to fetch loan folders: ${error.response?.data?.summary || error.message}`);
      allData.data.loanFolders = [];
      allData.summary.loanFolders = { error: error.message };
    }

    console.log('\n5. Attempting to fetch Loans (will likely fail due to permissions)...');
    
    // Try different loan endpoints
    const loanEndpoints = [
      { name: 'Direct Loans', url: `${baseUrl}/encompass/v1/loans`, params: { limit: 10 } },
      { name: 'Loan Applications', url: `${baseUrl}/encompass/v1/loanApplications`, params: { limit: 10 } },
      { name: 'Pipeline', url: `${baseUrl}/encompass/v1/pipeline`, params: { limit: 10 } }
    ];

    allData.data.loans = {};
    allData.summary.loans = {};

    for (const endpoint of loanEndpoints) {
      try {
        console.log(`   Testing: ${endpoint.name}`);
        
        const response = await axios.get(endpoint.url, {
          headers: headers,
          params: endpoint.params
        });

        const loans = response.data;
        console.log(`   ✅ ${endpoint.name}: ${loans.length} records`);
        
        allData.data.loans[endpoint.name.toLowerCase().replace(/\s+/g, '_')] = loans;
        allData.summary.loans[endpoint.name.toLowerCase().replace(/\s+/g, '_')] = loans.length;

        if (loans.length > 0) {
          // Save this successful loan data
          fs.writeFileSync(
            path.join(dataDir, `encompass-${endpoint.name.toLowerCase().replace(/\s+/g, '-')}.json`),
            JSON.stringify({
              timestamp: new Date().toISOString(),
              endpoint: endpoint.name,
              total: loans.length,
              loans: loans
            }, null, 2)
          );
          console.log(`   💾 ${endpoint.name} saved to: encompass-${endpoint.name.toLowerCase().replace(/\s+/g, '-')}.json`);
        }

      } catch (error) {
        const status = error.response?.status || 'ERROR';
        const message = error.response?.data?.summary || error.message;
        console.log(`   ❌ ${endpoint.name}: ${status} - ${message}`);
        
        allData.data.loans[endpoint.name.toLowerCase().replace(/\s+/g, '_')] = [];
        allData.summary.loans[endpoint.name.toLowerCase().replace(/\s+/g, '_')] = { error: message };
      }
    }

    console.log('\n6. Testing Additional Endpoints...');
    
    const additionalEndpoints = [
      { name: 'Current User', url: `${baseUrl}/encompass/v1/users/current` },
      { name: 'Borrowers', url: `${baseUrl}/encompass/v1/borrowers`, params: { limit: 10 } },
      { name: 'Loan Officers', url: `${baseUrl}/encompass/v1/loanOfficers`, params: { limit: 10 } },
      { name: 'Organizations', url: `${baseUrl}/encompass/v1/organizations`, params: { limit: 10 } },
      { name: 'Webhooks', url: `${baseUrl}/encompass/v1/webhooks` }
    ];

    allData.data.additional = {};
    allData.summary.additional = {};

    for (const endpoint of additionalEndpoints) {
      try {
        console.log(`   Testing: ${endpoint.name}`);
        
        const config = {
          headers: headers
        };
        if (endpoint.params) {
          config.params = endpoint.params;
        }

        const response = await axios.get(endpoint.url, config);
        const data = response.data;
        
        const count = Array.isArray(data) ? data.length : 1;
        console.log(`   ✅ ${endpoint.name}: ${count} records`);
        
        allData.data.additional[endpoint.name.toLowerCase().replace(/\s+/g, '_')] = data;
        allData.summary.additional[endpoint.name.toLowerCase().replace(/\s+/g, '_')] = count;

        // Save if we got data
        if (data && (Array.isArray(data) ? data.length > 0 : true)) {
          fs.writeFileSync(
            path.join(dataDir, `encompass-${endpoint.name.toLowerCase().replace(/\s+/g, '-')}.json`),
            JSON.stringify({
              timestamp: new Date().toISOString(),
              endpoint: endpoint.name,
              data: data
            }, null, 2)
          );
          console.log(`   💾 ${endpoint.name} saved to: encompass-${endpoint.name.toLowerCase().replace(/\s+/g, '-')}.json`);
        }

      } catch (error) {
        const status = error.response?.status || 'ERROR';
        const message = error.response?.data?.summary || error.message;
        console.log(`   ❌ ${endpoint.name}: ${status} - ${message}`);
        
        allData.data.additional[endpoint.name.toLowerCase().replace(/\s+/g, '_')] = null;
        allData.summary.additional[endpoint.name.toLowerCase().replace(/\s+/g, '_')] = { error: message };
      }
    }

    console.log('\n7. Saving Complete Data Summary...');
    
    // Save complete summary
    fs.writeFileSync(
      path.join(dataDir, 'encompass-complete-data-summary.json'),
      JSON.stringify(allData, null, 2)
    );
    console.log('💾 Complete summary saved to: encompass-complete-data-summary.json');

    console.log('\n📊 FINAL SUMMARY:');
    console.log('=================');
    console.log(`✅ Users: ${allData.summary.users.total || 0}`);
    console.log(`✅ Business Contacts: ${allData.summary.businessContacts.total || 0}`);
    console.log(`✅ Loan Folders: ${allData.summary.loanFolders.total || 0}`);
    
    const successfulLoans = Object.values(allData.summary.loans).filter(v => typeof v === 'number').reduce((a, b) => a + b, 0);
    console.log(`📋 Loans: ${successfulLoans} (limited by permissions)`);
    
    const successfulAdditional = Object.values(allData.summary.additional).filter(v => typeof v === 'number').length;
    console.log(`🔍 Additional Endpoints: ${successfulAdditional} accessible`);

    console.log('\n📁 All data files saved to ./data/ directory');
    console.log('🎯 Ready for GoHighLevel integration!');

  } catch (error) {
    console.error('❌ Failed to fetch Encompass data:', error.message);
    process.exit(1);
  }
}

// Run if this file is executed directly
if (require.main === module) {
  fetchAllEncompassData()
    .then(() => {
      console.log('\n✅ All Encompass data fetch completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Data fetch failed:', error);
      process.exit(1);
    });
}

module.exports = { fetchAllEncompassData };
