{"timestamp": "2025-07-04T17:51:20.031Z", "endpoints_tested": [{"endpoint": "V1 Get Borrower Contact List (POST)", "url": "/encompass/v1/borrowerContactSelector/", "status": "success", "response_size": 1000}, {"endpoint": "V1 Get a Borrower Contact (GET)", "url": "/encompass/v1/borrowerContacts/a26af9f3-2d41-4ae1-9a41-366437ff8aaa", "status": "success", "contact_id": "a26af9f3-2d41-4ae1-9a41-366437ff8aaa"}, {"endpoint": "V1 View Borrower Contacts (with Pa<PERSON><PERSON>)", "url": "/encompass/v1/borrowerContactSelector", "status": "success", "advanced_pagination": true}], "total_contacts_found": 1000, "pagination_details": {"borrower_contact_list": {"pages_tested": 1, "total_contacts": 1000, "pagination_working": false}}, "errors": []}