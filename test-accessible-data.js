const EncompassApi = require('./encompassApi');
const axios = require('axios');
const logger = require('./logger');
const fs = require('fs');
const path = require('path');

async function testAccessibleData() {
  console.log('🔍 Testing Accessible Encompass Data Sources...\n');

  try {
    // Initialize Encompass API
    const encompassApi = new EncompassApi();
    
    console.log('1. Authenticating with Encompass API...');
    const authResult = await encompassApi.authenticate();
    if (!authResult) {
      throw new Error('Failed to authenticate with Encompass API');
    }
    console.log('✅ Successfully authenticated\n');

    const baseUrl = 'https://api.elliemae.com';
    const headers = {
      'Authorization': `Bearer ${encompassApi.accessToken}`,
      'Content-Type': 'application/json'
    };

    console.log('2. Exploring Loan Folders (16 available)...');
    
    try {
      const foldersResponse = await axios.get(`${baseUrl}/encompass/v1/loanfolders`, {
        headers: headers
      });

      const folders = foldersResponse.data;
      console.log(`✅ Found ${folders.length} loan folders:`);
      
      folders.forEach((folder, index) => {
        console.log(`   ${index + 1}. ${folder.name} (ID: ${folder.id})`);
        if (folder.description) console.log(`      Description: ${folder.description}`);
      });

      console.log('\n3. Checking loans in each folder...');
      
      const allLeads = [];
      
      for (const folder of folders.slice(0, 5)) { // Test first 5 folders
        try {
          console.log(`\nChecking folder: ${folder.name}`);
          
          // Try to get loans from this folder
          const folderLoansResponse = await axios.get(`${baseUrl}/encompass/v1/loanfolders/${folder.id}/loans`, {
            headers: headers,
            params: {
              limit: 50
            }
          });

          const folderLoans = folderLoansResponse.data;
          console.log(`   ✅ Found ${folderLoans.length} loans in ${folder.name}`);
          
          if (folderLoans.length > 0) {
            // Get detailed loan data
            for (const loanId of folderLoans.slice(0, 3)) { // Get details for first 3 loans
              try {
                const loanDetailResponse = await axios.get(`${baseUrl}/encompass/v1/loans/${loanId}`, {
                  headers: headers,
                  params: {
                    entities: 'loan(1109,4000,4002,4003,4004,4008,4009,4010,4011,4012,4013,4014,4015,4016,4017,4018,4019,4020,4021,4022,4023,4024,4025,4026,4027,4028,4029,4030,4031,4032,4033,4034,4035,4036,4037,4038,4039,4040,4041,4042,4043,4044,4045,4046,4047,4048,4049,4050)'
                  }
                });

                const loanDetail = loanDetailResponse.data;
                console.log(`      📋 Loan ${loanId}: ${Object.keys(loanDetail).length} fields available`);
                
                allLeads.push({
                  folderId: folder.id,
                  folderName: folder.name,
                  loanId: loanId,
                  loanData: loanDetail
                });

              } catch (loanError) {
                console.log(`      ❌ Could not get details for loan ${loanId}: ${loanError.response?.data?.summary || loanError.message}`);
              }
            }
          }

        } catch (folderError) {
          console.log(`   ❌ Could not access folder ${folder.name}: ${folderError.response?.data?.summary || folderError.message}`);
        }
      }

      // Save loan folder data
      const dataDir = path.join(__dirname, 'data');
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }

      const foldersFile = path.join(dataDir, 'encompass-loan-folders.json');
      fs.writeFileSync(foldersFile, JSON.stringify({
        timestamp: new Date().toISOString(),
        totalFolders: folders.length,
        folders: folders,
        sampleLeads: allLeads
      }, null, 2));

      console.log(`\n💾 Loan folders data saved to: ${foldersFile}`);

    } catch (error) {
      console.log(`❌ Failed to access loan folders: ${error.response?.data?.summary || error.message}`);
    }

    console.log('\n4. Exploring Business Contacts (725 available)...');
    
    try {
      const contactsResponse = await axios.get(`${baseUrl}/encompass/v1/businessContacts`, {
        headers: headers,
        params: {
          limit: 50 // Get first 50 contacts
        }
      });

      const contacts = contactsResponse.data;
      console.log(`✅ Found ${contacts.length} business contacts (showing first 50):`);
      
      // Analyze contact structure
      if (contacts.length > 0) {
        const sampleContact = contacts[0];
        console.log('\n📊 Contact data structure:');
        console.log('   Available fields:', Object.keys(sampleContact));
        
        // Show some sample contacts
        console.log('\n👥 Sample contacts:');
        contacts.slice(0, 10).forEach((contact, index) => {
          console.log(`   ${index + 1}. ${contact.name || contact.firstName + ' ' + contact.lastName || 'Unknown'}`);
          console.log(`      Email: ${contact.email || 'N/A'}`);
          console.log(`      Phone: ${contact.phone || 'N/A'}`);
          console.log(`      Type: ${contact.contactType || 'N/A'}`);
        });

        // Save contacts data
        const contactsFile = path.join(dataDir, 'encompass-business-contacts.json');
        fs.writeFileSync(contactsFile, JSON.stringify({
          timestamp: new Date().toISOString(),
          totalContacts: contacts.length,
          sampleContacts: contacts.slice(0, 20), // Save first 20
          fieldAnalysis: analyzeContactFields(contacts)
        }, null, 2));

        console.log(`\n💾 Business contacts data saved to: ${contactsFile}`);
      }

    } catch (error) {
      console.log(`❌ Failed to access business contacts: ${error.response?.data?.summary || error.message}`);
    }

    console.log('\n5. Testing Alternative Lead Sources...');
    
    // Try other potential endpoints
    const alternativeEndpoints = [
      {
        name: 'Loan Applications',
        url: `${baseUrl}/encompass/v1/loanApplications`,
        params: { limit: 10 }
      },
      {
        name: 'Borrowers',
        url: `${baseUrl}/encompass/v1/borrowers`,
        params: { limit: 10 }
      },
      {
        name: 'Loan Officers',
        url: `${baseUrl}/encompass/v1/loanOfficers`,
        params: { limit: 10 }
      },
      {
        name: 'Pipeline',
        url: `${baseUrl}/encompass/v1/pipeline`,
        params: { limit: 10 }
      }
    ];

    for (const endpoint of alternativeEndpoints) {
      try {
        console.log(`\nTesting: ${endpoint.name}`);
        
        const response = await axios.get(endpoint.url, {
          headers: headers,
          params: endpoint.params
        });

        console.log(`✅ ${endpoint.name}: Found ${response.data.length || 'data'} records`);
        
        if (response.data.length > 0) {
          console.log(`   Sample fields: ${Object.keys(response.data[0]).slice(0, 5).join(', ')}`);
        }

      } catch (error) {
        const status = error.response?.status || 'ERROR';
        const message = error.response?.data?.summary || error.message;
        console.log(`❌ ${endpoint.name}: ${status} - ${message}`);
      }
    }

    console.log('\n6. Summary and Recommendations:');
    console.log('===============================');
    
    console.log('✅ Successfully accessed:');
    console.log('   - Loan Folders (16 folders available)');
    console.log('   - Business Contacts (725 contacts available)');
    console.log('   - User List (100 users available)');
    
    console.log('\n🎯 Best Integration Strategy:');
    console.log('1. Use Loan Folders to access loan/lead data');
    console.log('2. Use Business Contacts as supplementary lead source');
    console.log('3. Cross-reference with User data for assignments');
    
    console.log('\n📋 Next Steps for Integration:');
    console.log('1. Modify encompassApi.js to use loan folders endpoint');
    console.log('2. Map loan folder data to GoHighLevel lead format');
    console.log('3. Set up sync to check folders for new loans regularly');
    console.log('4. Consider business contacts as additional lead source');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

function analyzeContactFields(contacts) {
  const fieldStats = {};
  
  contacts.forEach(contact => {
    Object.entries(contact).forEach(([field, value]) => {
      if (!fieldStats[field]) {
        fieldStats[field] = {
          count: 0,
          sampleValues: [],
          dataTypes: new Set()
        };
      }
      
      if (value !== null && value !== undefined && value !== '') {
        fieldStats[field].count++;
        fieldStats[field].dataTypes.add(typeof value);
        
        if (fieldStats[field].sampleValues.length < 3) {
          fieldStats[field].sampleValues.push(String(value).substring(0, 50));
        }
      }
    });
  });
  
  // Convert Set to Array for JSON serialization
  Object.values(fieldStats).forEach(stats => {
    stats.dataTypes = [...stats.dataTypes];
  });
  
  return fieldStats;
}

// Run test if this file is executed directly
if (require.main === module) {
  testAccessibleData()
    .then(() => {
      console.log('\n✅ Accessible data test completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testAccessibleData };
