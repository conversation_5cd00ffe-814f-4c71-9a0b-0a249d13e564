{"summary": {"totalProcessed": 10, "created": 10, "updated": 0, "errors": 25, "apiCalls": 41, "elapsedTime": 19.325}, "processedContacts": [{"encompassId": "a26af9f3-2d41-4ae1-9a41-366437ff8aaa", "action": "created", "name": "BARBARA HERRERA VEGA", "email": "<EMAIL>", "phone": "************"}, {"encompassId": "7daa9d4a-5411-4a47-8650-0bf2946a5a4d", "action": "created", "name": "ORLANDO GIL", "email": "<EMAIL>", "phone": "************"}, {"encompassId": "95671b80-e118-4aa8-aaf9-cd55db29dbff", "action": "created", "name": "ARIANA RODRIGUEZ", "email": "<EMAIL>", "phone": "************"}, {"encompassId": "609bf4b4-c530-4d0f-bfa5-c5278fc5173d", "ghlContactId": "AV9SqXkAjsFVq6p1PCrl", "action": "created", "name": "MANUEL JUANES", "email": "<EMAIL>", "phone": "************"}, {"encompassId": "380a9e11-7207-410c-b5a7-d9277439cccf", "ghlContactId": "c25F3EGWhia0AEJ3NYZy", "action": "created", "name": "TONY MEDEROS", "email": "<EMAIL>", "phone": "************"}, {"encompassId": "ac709e4c-2a38-4b93-bcf2-6c6df13245dd", "ghlContactId": "fC0b5Q9m2RBfaMdUQAPj", "action": "created", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "************"}, {"encompassId": "773d01eb-c5e8-43a2-a5a6-028bec114ce9", "ghlContactId": "PNZ9T5RKEAfcFl5qmhIO", "action": "created", "name": "<PERSON>", "email": "<EMAIL>", "phone": "************"}, {"encompassId": "1142bbe1-b4f9-4c44-b757-eb6895748024", "action": "created", "name": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "************"}, {"encompassId": "d0323134-23a6-4b44-902f-52429fd6dc33", "ghlContactId": "0bHzj66hwvbiyHuOkYPr", "action": "created", "name": "<PERSON>", "email": "<EMAIL>", "phone": "************"}, {"encompassId": "e0c19257-b1d0-407a-bb63-652d647881d4", "action": "created", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "************"}], "errors": [{"description": "Searching for existing contact by email: <EMAIL>", "error": {"message": "Cannot POST /contacts/search/duplicate", "error": "Not Found", "statusCode": 404}}, {"description": "Searching for existing contact by phone: ************", "error": {"message": "Cannot POST /contacts/search/duplicate", "error": "Not Found", "statusCode": 404}}, {"description": "Creating new GHL contact: BARBARA HERRERA VEGA", "error": {"statusCode": 400, "message": "This location does not allow duplicated contacts.", "meta": {"contactId": "ieJuapqR19A22spHPIij", "matchingField": "email"}, "traceId": "62339e84-8844-936f-b0e8-f4af8d2bb92a"}}, {"description": "Searching for existing contact by email: <EMAIL>", "error": {"message": "Cannot POST /contacts/search/duplicate", "error": "Not Found", "statusCode": 404}}, {"description": "Searching for existing contact by phone: ************", "error": {"message": "Cannot POST /contacts/search/duplicate", "error": "Not Found", "statusCode": 404}}, {"description": "Creating new GHL contact: ORLANDO GIL", "error": {"statusCode": 400, "message": "This location does not allow duplicated contacts.", "meta": {"contactId": "F7HQZG4VBTV2tZlruFZp", "matchingField": "email"}, "traceId": "5c293124-e9c5-48d6-8422-8c41c513994f"}}, {"description": "Searching for existing contact by email: a<PERSON><PERSON>drig<PERSON><EMAIL>", "error": {"message": "Cannot POST /contacts/search/duplicate", "error": "Not Found", "statusCode": 404}}, {"description": "Searching for existing contact by phone: ************", "error": {"message": "Cannot POST /contacts/search/duplicate", "error": "Not Found", "statusCode": 404}}, {"description": "Creating new GHL contact: ARIANA RODRIGUEZ", "error": {"statusCode": 400, "message": "This location does not allow duplicated contacts.", "meta": {"contactId": "LN2gHsOmq9inhCkQkLdr", "matchingField": "email"}, "traceId": "27ea2c03-8e54-47ac-a323-9f56849634b3"}}, {"description": "Searching for existing contact by email: <EMAIL>", "error": {"message": "Cannot POST /contacts/search/duplicate", "error": "Not Found", "statusCode": 404}}, {"description": "Searching for existing contact by phone: ************", "error": {"message": "Cannot POST /contacts/search/duplicate", "error": "Not Found", "statusCode": 404}}, {"description": "Searching for existing contact by email: <EMAIL>", "error": {"message": "Cannot POST /contacts/search/duplicate", "error": "Not Found", "statusCode": 404}}, {"description": "Searching for existing contact by phone: ************", "error": {"message": "Cannot POST /contacts/search/duplicate", "error": "Not Found", "statusCode": 404}}, {"description": "Searching for existing contact by email: ginn<PERSON><PERSON><PERSON>@onestoprealty.com", "error": {"message": "Cannot POST /contacts/search/duplicate", "error": "Not Found", "statusCode": 404}}, {"description": "Searching for existing contact by phone: ************", "error": {"message": "Cannot POST /contacts/search/duplicate", "error": "Not Found", "statusCode": 404}}, {"description": "Searching for existing contact by email: <EMAIL>", "error": {"message": "Cannot POST /contacts/search/duplicate", "error": "Not Found", "statusCode": 404}}, {"description": "Searching for existing contact by phone: ************", "error": {"message": "Cannot POST /contacts/search/duplicate", "error": "Not Found", "statusCode": 404}}, {"description": "Searching for existing contact by email: kenia.rod<PERSON><PERSON><PERSON>@chase.com", "error": {"message": "Cannot POST /contacts/search/duplicate", "error": "Not Found", "statusCode": 404}}, {"description": "Searching for existing contact by phone: ************", "error": {"message": "Cannot POST /contacts/search/duplicate", "error": "Not Found", "statusCode": 404}}, {"description": "Creating new GHL contact: <PERSON><PERSON>", "error": {"statusCode": 400, "message": "This location does not allow duplicated contacts.", "meta": {"contactId": "PNZ9T5RKEAfcFl5qmhIO", "matchingField": "phone"}, "traceId": "c3faf946-04d9-4a95-8df4-441b104ade9b"}}, {"description": "Searching for existing contact by email: <EMAIL>", "error": {"message": "Cannot POST /contacts/search/duplicate", "error": "Not Found", "statusCode": 404}}, {"description": "Searching for existing contact by phone: ************", "error": {"message": "Cannot POST /contacts/search/duplicate", "error": "Not Found", "statusCode": 404}}, {"description": "Searching for existing contact by email: <EMAIL>", "error": {"message": "Cannot POST /contacts/search/duplicate", "error": "Not Found", "statusCode": 404}}, {"description": "Searching for existing contact by phone: ************", "error": {"message": "Cannot POST /contacts/search/duplicate", "error": "Not Found", "statusCode": 404}}, {"description": "Creating new GHL contact: <PERSON><PERSON><PERSON>", "error": {"statusCode": 400, "message": "This location does not allow duplicated contacts.", "meta": {"contactId": "0bHzj66hwvbiyHuOkYPr", "matchingField": "email"}, "traceId": "2b720e11-1341-4b7a-9a85-f85d0a8791f3"}}]}