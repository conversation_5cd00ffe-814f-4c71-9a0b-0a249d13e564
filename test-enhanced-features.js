const { validateConfig } = require('./config');
const GoHighLevelApi = require('./gohighlevelApi');
const leadDatabase = require('./leadDatabase');
const logger = require('./logger');

async function testEnhancedFeatures() {
  console.log('🧪 Testing Enhanced Features...\n');

  try {
    // Test 1: Configuration validation
    console.log('1. Testing configuration validation...');
    validateConfig();
    console.log('✅ Configuration is valid\n');

    // Test 2: GoHighLevel API initialization
    console.log('2. Testing GoHighLevel API initialization...');
    const ghlApi = new GoHighLevelApi();
    console.log('✅ GoHighLevel API initialized\n');

    // Test 3: Data validation and generation
    console.log('3. Testing data validation and generation...');
    
    // Test email validation
    const validEmail = ghlApi.validateAndCleanEmail('<EMAIL>');
    const invalidEmail = ghlApi.validateAndCleanEmail('invalid-email');
    console.log(`   Valid email test: ${validEmail} ✅`);
    console.log(`   Invalid email test: ${invalidEmail === null ? 'null (correct)' : 'failed'} ✅`);

    // Test phone validation
    const validPhone = ghlApi.validateAndCleanPhone('(*************');
    const invalidPhone = ghlApi.validateAndCleanPhone('123');
    console.log(`   Valid phone test: ${validPhone} ✅`);
    console.log(`   Invalid phone test: ${invalidPhone === null ? 'null (correct)' : 'failed'} ✅`);

    // Test placeholder generation
    const testLead = { id: 'test123', firstName: 'John', lastName: 'Doe' };
    const placeholderEmail = ghlApi.generatePlaceholderEmail(testLead);
    const placeholderPhone = ghlApi.generatePlaceholderPhone(testLead);
    console.log(`   Generated email: ${placeholderEmail} ✅`);
    console.log(`   Generated phone: ${placeholderPhone} ✅\n`);

    // Test 4: Lead data formatting
    console.log('4. Testing lead data formatting...');
    const sampleLead = {
      id: 'test-lead-123',
      firstName: 'Jane',
      lastName: 'Smith',
      email: 'invalid-email',
      phone: '123',
      address: '123 Main St',
      city: 'Anytown',
      state: 'CA',
      zipCode: '12345',
      loanAmount: 350000,
      loanPurpose: 'Purchase'
    };

    const formattedLead = ghlApi.formatLeadForGoHighLevel(sampleLead);
    console.log('   Formatted lead data:');
    console.log(`   - Name: ${formattedLead.name}`);
    console.log(`   - Email: ${formattedLead.email} ${formattedLead.tags.includes('Generated Email') ? '(generated)' : ''}`);
    console.log(`   - Phone: ${formattedLead.phone} ${formattedLead.tags.includes('Generated Phone') ? '(generated)' : ''}`);
    console.log(`   - Tags: ${formattedLead.tags.join(', ')}`);
    console.log('✅ Lead formatting working correctly\n');

    // Test 5: Lead database operations
    console.log('5. Testing lead database operations...');
    
    // Load database
    let db = leadDatabase.load();
    console.log(`   Initial processed leads count: ${leadDatabase.getProcessedLeadsCount(db)}`);

    // Test marking a lead as processed
    leadDatabase.markLeadProcessed(db, 'test-lead-456', 'ghl-contact-789', 'created', 'opportunity-123');
    console.log('   ✅ Marked test lead as processed');

    // Test checking if lead is processed
    const isProcessed = leadDatabase.isLeadProcessed(db, 'test-lead-456');
    console.log(`   Lead processed check: ${isProcessed ? 'true' : 'false'} ✅`);

    // Test getting processed lead info
    const leadInfo = leadDatabase.getProcessedLeadInfo(db, 'test-lead-456');
    console.log(`   Lead info: Contact ID ${leadInfo.ghlContactId}, Action: ${leadInfo.action} ✅`);

    // Test statistics
    const stats = leadDatabase.getStats(db);
    console.log(`   Stats: ${stats.totalProcessed} processed, ${stats.totalCreated} created ✅`);

    // Test recent activity
    const recentActivity = leadDatabase.getRecentActivity(db, 3);
    console.log(`   Recent activity: ${recentActivity.length} entries ✅`);

    // Save database
    const saved = leadDatabase.save(db);
    console.log(`   Database save: ${saved ? 'success' : 'failed'} ✅\n`);

    // Test 6: Error handling
    console.log('6. Testing error handling...');
    
    // Test marking a lead as failed
    leadDatabase.markLeadFailed(db, 'failed-lead-789', 'Test error message');
    const failedLeadInfo = leadDatabase.getProcessedLeadInfo(db, 'failed-lead-789');
    console.log(`   Failed lead status: ${failedLeadInfo.status} ✅`);
    console.log(`   Failed lead error: ${failedLeadInfo.error} ✅\n`);

    console.log('🎉 All enhanced features tested successfully!');
    console.log('\n📊 Final Statistics:');
    const finalStats = leadDatabase.getStats(db);
    console.log(`   - Total Processed: ${finalStats.totalProcessed}`);
    console.log(`   - Total Created: ${finalStats.totalCreated}`);
    console.log(`   - Total Updated: ${finalStats.totalUpdated}`);
    console.log(`   - Total Errors: ${finalStats.totalErrors}`);
    console.log(`   - Total Opportunities: ${finalStats.totalOpportunities}`);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  testEnhancedFeatures()
    .then(() => {
      console.log('\n✅ All tests completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Tests failed:', error);
      process.exit(1);
    });
}

module.exports = { testEnhancedFeatures };
