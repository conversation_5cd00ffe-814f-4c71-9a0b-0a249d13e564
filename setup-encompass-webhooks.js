const axios = require('axios');
require('dotenv').config();

// Configuration
const baseUrl = 'https://api.elliemae.com';
const username = process.env.ENCOMPASS_USERNAME;
const password = process.env.ENCOMPASS_PASSWORD;
const clientId = process.env.ENCOMPASS_CLIENT_ID;
const clientSecret = process.env.ENCOMPASS_CLIENT_SECRET;
const webhookCallbackUrl = process.env.WEBHOOK_CALLBACK_URL || 'http://localhost:3000/webhook/encompass';

console.log('🔧 Encompass Webhook Subscription Setup');
console.log('='.repeat(50));

// Authentication
async function getAccessToken() {
    try {
        console.log('🔐 Authenticating with Encompass API...');
        
        const response = await axios.post(`${baseUrl}/oauth2/v1/token`, {
            grant_type: 'password',
            username: username,
            password: password,
            client_id: clientId,
            client_secret: clientSecret
        }, {
            headers: {
                'Content-Type': 'application/json'
            }
        });

        console.log('✅ Authentication successful');
        return response.data.access_token;
    } catch (error) {
        console.error('❌ Authentication failed:', error.response?.data || error.message);
        throw error;
    }
}

// Get available webhook resources
async function getWebhookResources(token) {
    try {
        console.log('\n📋 Fetching available webhook resources...');
        
        const response = await axios.get(`${baseUrl}/webhook/v1/resources`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        console.log('✅ Available resources:');
        response.data.forEach(resource => {
            console.log(`  - ${resource.name}: ${resource.description || 'No description'}`);
        });

        return response.data;
    } catch (error) {
        console.error('❌ Error fetching webhook resources:', error.response?.data || error.message);
        throw error;
    }
}

// Get events for a specific resource
async function getResourceEvents(token, resourceName) {
    try {
        console.log(`\n🎯 Fetching events for resource: ${resourceName}`);
        
        const response = await axios.get(`${baseUrl}/webhook/v1/resources/${resourceName}/events`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        console.log(`✅ Available events for ${resourceName}:`);
        response.data.forEach(event => {
            console.log(`  - ${event.name}: ${event.description || 'No description'}`);
        });

        return response.data;
    } catch (error) {
        console.error(`❌ Error fetching events for ${resourceName}:`, error.response?.data || error.message);
        return [];
    }
}

// Get existing subscriptions
async function getExistingSubscriptions(token) {
    try {
        console.log('\n📡 Fetching existing webhook subscriptions...');
        
        const response = await axios.get(`${baseUrl}/webhook/v1/subscriptions`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        console.log('✅ Existing subscriptions:');
        if (response.data.length === 0) {
            console.log('  No existing subscriptions found');
        } else {
            response.data.forEach(sub => {
                console.log(`  - ID: ${sub.subscriptionId}`);
                console.log(`    Resource: ${sub.resource}`);
                console.log(`    Events: ${sub.events.join(', ')}`);
                console.log(`    Endpoint: ${sub.endpoint}`);
                console.log(`    Status: ${sub.status}`);
                console.log('');
            });
        }

        return response.data;
    } catch (error) {
        console.error('❌ Error fetching existing subscriptions:', error.response?.data || error.message);
        return [];
    }
}

// Create webhook subscription
async function createWebhookSubscription(token, resource, events, endpoint) {
    try {
        console.log(`\n🔗 Creating webhook subscription for ${resource}...`);
        
        const subscriptionData = {
            resource: resource,
            events: events,
            endpoint: endpoint,
            signingKey: process.env.ENCOMPASS_WEBHOOK_SIGNING_KEY || 'default-signing-key-change-this'
        };

        console.log('📝 Subscription data:', JSON.stringify(subscriptionData, null, 2));

        const response = await axios.post(`${baseUrl}/webhook/v1/subscriptions`, subscriptionData, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        console.log('✅ Webhook subscription created successfully!');
        console.log('📋 Subscription details:', JSON.stringify(response.data, null, 2));
        
        return response.data;
    } catch (error) {
        console.error('❌ Error creating webhook subscription:', error.response?.data || error.message);
        throw error;
    }
}

// Delete webhook subscription
async function deleteWebhookSubscription(token, subscriptionId) {
    try {
        console.log(`\n🗑️ Deleting webhook subscription: ${subscriptionId}`);
        
        await axios.delete(`${baseUrl}/webhook/v1/subscriptions/${subscriptionId}`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        console.log('✅ Webhook subscription deleted successfully!');
    } catch (error) {
        console.error('❌ Error deleting webhook subscription:', error.response?.data || error.message);
        throw error;
    }
}

// Main setup function
async function setupWebhooks() {
    try {
        // Get access token
        const token = await getAccessToken();

        // Get available resources
        const resources = await getWebhookResources(token);

        // Check for user-related resources
        const userResources = resources.filter(r => 
            r.name.toLowerCase().includes('user') || 
            r.name.toLowerCase().includes('contact') ||
            r.name.toLowerCase().includes('borrower')
        );

        console.log('\n🎯 User-related resources found:');
        userResources.forEach(resource => {
            console.log(`  - ${resource.name}`);
        });

        // Get events for relevant resources
        for (const resource of userResources) {
            await getResourceEvents(token, resource.name);
        }

        // Get existing subscriptions
        const existingSubscriptions = await getExistingSubscriptions(token);

        // Setup subscriptions for External Users and Internal Users
        const subscriptionsToCreate = [
            {
                resource: 'ExternalUsers',
                events: ['create', 'update'],
                description: 'External users (potential borrowers)'
            },
            {
                resource: 'InternalUsers', 
                events: ['create', 'update'],
                description: 'Internal users'
            }
        ];

        console.log('\n🚀 Setting up webhook subscriptions...');
        
        for (const subscription of subscriptionsToCreate) {
            // Check if subscription already exists
            const existing = existingSubscriptions.find(sub => 
                sub.resource === subscription.resource && 
                sub.endpoint === webhookCallbackUrl
            );

            if (existing) {
                console.log(`⚠️ Subscription for ${subscription.resource} already exists (ID: ${existing.subscriptionId})`);
                console.log('   Delete it first if you want to recreate it.');
            } else {
                try {
                    await createWebhookSubscription(
                        token, 
                        subscription.resource, 
                        subscription.events, 
                        webhookCallbackUrl
                    );
                } catch (error) {
                    console.error(`❌ Failed to create subscription for ${subscription.resource}`);
                }
            }
        }

        console.log('\n🎉 Webhook setup completed!');
        console.log('\n📋 Next steps:');
        console.log('1. Start your webhook server: node encompass-webhook-integration.js');
        console.log('2. Make sure your webhook endpoint is publicly accessible');
        console.log('3. Test by creating a new borrower contact in Encompass');
        console.log('4. Check the webhook logs and GoHighLevel for new contacts');

    } catch (error) {
        console.error('❌ Setup failed:', error.message);
        process.exit(1);
    }
}

// Command line interface
const args = process.argv.slice(2);
const command = args[0];

if (command === 'list') {
    // List existing subscriptions
    getAccessToken().then(token => {
        return getExistingSubscriptions(token);
    }).catch(console.error);
} else if (command === 'delete' && args[1]) {
    // Delete specific subscription
    const subscriptionId = args[1];
    getAccessToken().then(token => {
        return deleteWebhookSubscription(token, subscriptionId);
    }).catch(console.error);
} else if (command === 'resources') {
    // List available resources
    getAccessToken().then(token => {
        return getWebhookResources(token);
    }).catch(console.error);
} else {
    // Default: setup webhooks
    setupWebhooks();
}
