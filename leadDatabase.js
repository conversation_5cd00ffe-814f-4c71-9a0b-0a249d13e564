const fs = require('fs');
const path = require('path');
const logger = require('./logger');

class LeadDatabase {
  constructor() {
    this.dbPath = path.join(__dirname, 'data', 'processed-leads.json');
    this.ensureDataDirectory();
  }

  ensureDataDirectory() {
    const dataDir = path.dirname(this.dbPath);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
  }

  // Load the database from disk or create a new one if it doesn't exist
  load() {
    try {
      if (fs.existsSync(this.dbPath)) {
        const data = fs.readFileSync(this.dbPath, 'utf8');
        return JSON.parse(data);
      }
      // Initialize with empty structure if file doesn't exist
      return {
        lastCount: 0,
        lastSync: new Date().toISOString(),
        processedLeads: {}, // Track processed lead IDs with their GoHighLevel contact IDs
        stats: {
          totalProcessed: 0,
          totalCreated: 0,
          totalUpdated: 0,
          totalErrors: 0,
          totalOpportunities: 0
        }
      };
    } catch (error) {
      logger.error(`Error loading lead database: ${error.message}`);
      // Return empty structure on error
      return {
        lastCount: 0,
        lastSync: new Date().toISOString(),
        processedLeads: {},
        stats: {
          totalProcessed: 0,
          totalCreated: 0,
          totalUpdated: 0,
          totalErrors: 0,
          totalOpportunities: 0
        }
      };
    }
  }

  // Save the database to disk
  save(db) {
    try {
      this.ensureDataDirectory();

      // Update timestamp
      db.lastSync = new Date().toISOString();

      // Write to file
      fs.writeFileSync(this.dbPath, JSON.stringify(db, null, 2), 'utf8');
      logger.info(`Lead database updated. Total processed: ${db.stats.totalProcessed}`);
      return true;
    } catch (error) {
      logger.error(`Error saving lead database: ${error.message}`);
      return false;
    }
  }

  // Check if a lead has already been processed
  isLeadProcessed(db, leadId) {
    if (!db.processedLeads) {
      db.processedLeads = {};
    }
    return !!db.processedLeads[leadId];
  }

  // Mark a lead as processed with their GoHighLevel contact ID
  markLeadProcessed(db, leadId, ghlContactId, action = 'created', opportunityId = null) {
    if (!db.processedLeads) {
      db.processedLeads = {};
    }
    
    db.processedLeads[leadId] = {
      ghlContactId: ghlContactId,
      processedAt: new Date().toISOString(),
      action: action, // 'created' or 'updated'
      status: 'completed',
      opportunityId: opportunityId
    };

    // Update stats
    if (!db.stats) {
      db.stats = {
        totalProcessed: 0,
        totalCreated: 0,
        totalUpdated: 0,
        totalErrors: 0,
        totalOpportunities: 0
      };
    }

    db.stats.totalProcessed++;
    if (action === 'created') {
      db.stats.totalCreated++;
    } else if (action === 'updated') {
      db.stats.totalUpdated++;
    }

    if (opportunityId) {
      db.stats.totalOpportunities++;
    }

    return db;
  }

  // Mark a lead as failed
  markLeadFailed(db, leadId, error) {
    if (!db.processedLeads) {
      db.processedLeads = {};
    }
    
    db.processedLeads[leadId] = {
      ghlContactId: null,
      processedAt: new Date().toISOString(),
      action: 'failed',
      status: 'error',
      error: error
    };

    // Update stats
    if (!db.stats) {
      db.stats = {
        totalProcessed: 0,
        totalCreated: 0,
        totalUpdated: 0,
        totalErrors: 0,
        totalOpportunities: 0
      };
    }

    db.stats.totalErrors++;
    return db;
  }

  // Get processed lead info
  getProcessedLeadInfo(db, leadId) {
    if (!db.processedLeads) {
      return null;
    }
    return db.processedLeads[leadId] || null;
  }

  // Get count of processed leads
  getProcessedLeadsCount(db) {
    if (!db.processedLeads) {
      return 0;
    }
    return Object.keys(db.processedLeads).length;
  }

  // Get statistics
  getStats(db) {
    return db.stats || {
      totalProcessed: 0,
      totalCreated: 0,
      totalUpdated: 0,
      totalErrors: 0,
      totalOpportunities: 0
    };
  }

  // Clean up old entries (optional - keep only last 30 days)
  cleanup(db, daysToKeep = 30) {
    if (!db.processedLeads) {
      return db;
    }

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    let removedCount = 0;
    Object.keys(db.processedLeads).forEach(leadId => {
      const processedAt = new Date(db.processedLeads[leadId].processedAt);
      if (processedAt < cutoffDate) {
        delete db.processedLeads[leadId];
        removedCount++;
      }
    });

    if (removedCount > 0) {
      logger.info(`Cleaned up ${removedCount} old lead records`);
    }

    return db;
  }

  // Get recent activity (last N leads)
  getRecentActivity(db, limit = 10) {
    if (!db.processedLeads) {
      return [];
    }

    const leads = Object.entries(db.processedLeads)
      .map(([leadId, info]) => ({ leadId, ...info }))
      .sort((a, b) => new Date(b.processedAt) - new Date(a.processedAt))
      .slice(0, limit);

    return leads;
  }
}

module.exports = new LeadDatabase();
