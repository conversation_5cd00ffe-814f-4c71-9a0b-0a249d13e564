const axios = require('axios');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

console.log('🚀 Encompass to GoHighLevel Integration');
console.log('='.repeat(60));
console.log('📋 Features:');
console.log('   ✅ Fetch borrower data from Encompass API');
console.log('   ✅ Push to GoHighLevel with custom fields');
console.log('   ✅ Use borrower email and phone as primary identifiers');
console.log('   ✅ Map all available data fields');
console.log('='.repeat(60));

// Configuration
const encompassBaseUrl = 'https://api.elliemae.com';
const ghlBaseUrl = 'https://services.leadconnectorhq.com';

// Encompass credentials
const encompassUsername = process.env.ENCOMPASS_USERNAME;
const encompassPassword = process.env.ENCOMPASS_PASSWORD;
const encompassClientId = process.env.ENCOMPASS_CLIENT_ID;
const encompassClientSecret = process.env.ENCOMPASS_CLIENT_SECRET;

// GoHighLevel credentials
const ghlApiKey = process.env.GOHIGHLEVEL_API_KEY;
const ghlLocationId = process.env.GOHIGHLEVEL_LOCATION_ID;

// Ensure data directory exists
const dataDir = path.join(__dirname, 'data');
if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
}

class EncompassToGHLIntegration {
    constructor() {
        this.apiCallCount = 0;
        this.startTime = Date.now();
        this.processedContacts = [];
        this.errors = [];
    }

    // Get Encompass access token
    async getEncompassToken() {
        try {
            console.log('🔐 Getting Encompass access token...');

            const tokenResponse = await axios.post(`${encompassBaseUrl}/oauth2/v1/token`,
                `grant_type=password&username=${encodeURIComponent(encompassUsername)}&password=${encodeURIComponent(encompassPassword)}`,
                {
                    headers: {
                        'Authorization': `Basic ${Buffer.from(`${encompassClientId}:${encompassClientSecret}`).toString('base64')}`,
                        'Content-Type': 'application/x-www-form-urlencoded'
                    }
                }
            );

            console.log('✅ Encompass access token obtained');
            return tokenResponse.data.access_token;
        } catch (error) {
            console.error('❌ Error getting Encompass token:', error.response?.data || error.message);
            throw error;
        }
    }

    // API call wrapper with rate limiting
    async makeApiCall(url, options, description) {
        this.apiCallCount++;
        console.log(`🌐 API Call ${this.apiCallCount}: ${description}`);
        
        try {
            const response = await axios(url, options);
            await new Promise(resolve => setTimeout(resolve, 200)); // Rate limiting
            return response.data;
        } catch (error) {
            console.error(`❌ ${description} failed:`, error.response?.data || error.message);
            this.errors.push({ description, error: error.response?.data || error.message });
            return null;
        }
    }

    // Get borrower contact IDs from Encompass
    async getEncompassContactIds(token, limit = 10) {
        const data = await this.makeApiCall(
            `${encompassBaseUrl}/encompass/v1/borrowerContactSelector`,
            {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                data: { start: 1, limit: limit }
            },
            `Getting ${limit} Encompass borrower contact IDs`
        );
        return data || [];
    }

    // Get detailed borrower contact information from Encompass
    async getEncompassContactDetails(contactId, token) {
        return await this.makeApiCall(
            `${encompassBaseUrl}/encompass/v1/borrowerContacts/${contactId}`,
            {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            },
            `Getting Encompass contact details for ${contactId}`
        );
    }

    // Transform Encompass data to GoHighLevel format
    transformToGHLFormat(encompassContact) {
        const phones = [];
        if (encompassContact.homePhone) phones.push(encompassContact.homePhone);
        if (encompassContact.workPhone) phones.push(encompassContact.workPhone);
        if (encompassContact.mobilePhone) phones.push(encompassContact.mobilePhone);

        // Primary phone (first available)
        const primaryPhone = phones[0] || '';

        // Full address
        const address = encompassContact.currentMailingAddress;
        const fullAddress = address ? 
            `${address.street1 || ''} ${address.street2 || ''}, ${address.city || ''}, ${address.state || ''} ${address.zip || ''}`.trim() : '';

        return {
            // Standard GoHighLevel fields
            firstName: encompassContact.firstName || '',
            lastName: encompassContact.lastName || '',
            email: encompassContact.personalEmail || encompassContact.businessEmail || '',
            phone: primaryPhone,
            address1: address?.street1 || '',
            city: address?.city || '',
            state: address?.state || '',
            postalCode: address?.zip || '',
            
            // Custom fields for additional data
            customFields: [
                { key: 'encompass_contact_id', value: encompassContact.id || '' },
                { key: 'date_of_birth', value: encompassContact.birthdate || '' },
                { key: 'home_phone', value: encompassContact.homePhone || '' },
                { key: 'work_phone', value: encompassContact.workPhone || '' },
                { key: 'mobile_phone', value: encompassContact.mobilePhone || '' },
                { key: 'business_email', value: encompassContact.businessEmail || '' },
                { key: 'personal_email', value: encompassContact.personalEmail || '' },
                { key: 'full_address', value: fullAddress },
                { key: 'realtor_name', value: encompassContact.referral || '' },
                { key: 'source', value: 'Encompass API' },
                { key: 'import_date', value: new Date().toISOString() },
                
                // Placeholder fields for loan data (when available)
                { key: 'interest_rate', value: '' },
                { key: 'closing_date', value: '' },
                { key: 'loan_originator', value: '' },
                { key: 'realtor_phone', value: '' },
                { key: 'realtor_email', value: '' },
                { key: 'property_address', value: '' }
            ],
            
            // Tags for organization
            tags: ['Encompass Import', 'Mortgage Lead', 'Borrower']
        };
    }

    // Create or update contact in GoHighLevel
    async createOrUpdateGHLContact(contactData) {
        try {
            // First, try to find existing contact by email or phone
            let existingContact = null;
            
            if (contactData.email) {
                const searchByEmail = await this.makeApiCall(
                    `${ghlBaseUrl}/contacts/search/duplicate`,
                    {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${ghlApiKey}`,
                            'Content-Type': 'application/json',
                            'Version': '2021-07-28'
                        },
                        data: {
                            locationId: ghlLocationId,
                            email: contactData.email
                        }
                    },
                    `Searching for existing contact by email: ${contactData.email}`
                );
                
                if (searchByEmail && searchByEmail.contact) {
                    existingContact = searchByEmail.contact;
                }
            }

            // If not found by email, try by phone
            if (!existingContact && contactData.phone) {
                const searchByPhone = await this.makeApiCall(
                    `${ghlBaseUrl}/contacts/search/duplicate`,
                    {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${ghlApiKey}`,
                            'Content-Type': 'application/json',
                            'Version': '2021-07-28'
                        },
                        data: {
                            locationId: ghlLocationId,
                            phone: contactData.phone
                        }
                    },
                    `Searching for existing contact by phone: ${contactData.phone}`
                );
                
                if (searchByPhone && searchByPhone.contact) {
                    existingContact = searchByPhone.contact;
                }
            }

            // Prepare contact data for GoHighLevel
            const ghlContactData = {
                locationId: ghlLocationId,
                ...contactData
            };

            if (existingContact) {
                // Update existing contact
                const updatedContact = await this.makeApiCall(
                    `${ghlBaseUrl}/contacts/${existingContact.id}`,
                    {
                        method: 'PUT',
                        headers: {
                            'Authorization': `Bearer ${ghlApiKey}`,
                            'Content-Type': 'application/json',
                            'Version': '2021-07-28'
                        },
                        data: ghlContactData
                    },
                    `Updating existing GHL contact: ${contactData.firstName} ${contactData.lastName}`
                );

                return { action: 'updated', contact: updatedContact, contactId: existingContact.id };
            } else {
                // Create new contact
                const newContact = await this.makeApiCall(
                    `${ghlBaseUrl}/contacts/`,
                    {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${ghlApiKey}`,
                            'Content-Type': 'application/json',
                            'Version': '2021-07-28'
                        },
                        data: ghlContactData
                    },
                    `Creating new GHL contact: ${contactData.firstName} ${contactData.lastName}`
                );

                return { action: 'created', contact: newContact, contactId: newContact?.contact?.id };
            }

        } catch (error) {
            console.error(`❌ Error creating/updating GHL contact:`, error.response?.data || error.message);
            this.errors.push({ 
                description: `GHL contact operation for ${contactData.firstName} ${contactData.lastName}`, 
                error: error.response?.data || error.message 
            });
            return null;
        }
    }

    // Main integration function
    async runIntegration(contactLimit = 10) {
        console.log(`🚀 Starting Encompass to GoHighLevel integration for ${contactLimit} contacts...\n`);

        try {
            // Step 1: Get Encompass data
            console.log('📥 Step 1: Fetching data from Encompass...');
            const encompassToken = await this.getEncompassToken();
            
            const contactIds = await this.getEncompassContactIds(encompassToken, contactLimit);
            console.log(`✅ Retrieved ${contactIds.length} contact IDs from Encompass`);

            // Step 2: Process each contact
            console.log('\n🔄 Step 2: Processing and pushing to GoHighLevel...');
            
            for (let i = 0; i < Math.min(contactIds.length, contactLimit); i++) {
                const contactId = contactIds[i].id;
                console.log(`\n📄 Processing contact ${i + 1}/${Math.min(contactIds.length, contactLimit)}: ${contactId}`);

                // Get detailed contact info from Encompass
                const encompassContact = await this.getEncompassContactDetails(contactId, encompassToken);
                if (!encompassContact) {
                    console.log(`   ❌ Failed to get Encompass contact details`);
                    continue;
                }

                console.log(`   ✅ Encompass: ${encompassContact.firstName || ''} ${encompassContact.lastName || ''}`);
                console.log(`   📧 Email: ${encompassContact.personalEmail || encompassContact.businessEmail || 'N/A'}`);
                console.log(`   📞 Phone: ${encompassContact.homePhone || encompassContact.workPhone || encompassContact.mobilePhone || 'N/A'}`);

                // Transform to GoHighLevel format
                const ghlContactData = this.transformToGHLFormat(encompassContact);

                // Push to GoHighLevel
                const ghlResult = await this.createOrUpdateGHLContact(ghlContactData);
                
                if (ghlResult) {
                    console.log(`   ✅ GoHighLevel: Contact ${ghlResult.action} successfully`);
                    console.log(`   🆔 GHL Contact ID: ${ghlResult.contactId || 'N/A'}`);
                    
                    this.processedContacts.push({
                        encompassId: contactId,
                        ghlContactId: ghlResult.contactId,
                        action: ghlResult.action,
                        name: `${encompassContact.firstName || ''} ${encompassContact.lastName || ''}`.trim(),
                        email: encompassContact.personalEmail || encompassContact.businessEmail,
                        phone: encompassContact.homePhone || encompassContact.workPhone || encompassContact.mobilePhone
                    });
                } else {
                    console.log(`   ❌ Failed to create/update in GoHighLevel`);
                }
            }

            // Step 3: Summary and results
            console.log('\n📊 Step 3: Integration Summary...');
            this.generateSummary();

            return {
                processedContacts: this.processedContacts,
                errors: this.errors,
                apiCallCount: this.apiCallCount,
                elapsedTime: (Date.now() - this.startTime) / 1000
            };

        } catch (error) {
            console.error('❌ Integration failed:', error.message);
            throw error;
        }
    }

    // Generate integration summary
    generateSummary() {
        const created = this.processedContacts.filter(c => c.action === 'created').length;
        const updated = this.processedContacts.filter(c => c.action === 'updated').length;
        
        console.log('🎉 Integration completed!');
        console.log('='.repeat(60));
        console.log(`📥 Total contacts processed: ${this.processedContacts.length}`);
        console.log(`✨ New contacts created: ${created}`);
        console.log(`🔄 Existing contacts updated: ${updated}`);
        console.log(`❌ Errors encountered: ${this.errors.length}`);
        console.log(`🌐 Total API calls made: ${this.apiCallCount}`);
        console.log(`⏱️ Total time: ${((Date.now() - this.startTime) / 1000).toFixed(2)} seconds`);
        console.log('='.repeat(60));

        if (this.errors.length > 0) {
            console.log('\n⚠️ Errors encountered:');
            this.errors.forEach((error, index) => {
                console.log(`   ${index + 1}. ${error.description}: ${JSON.stringify(error.error)}`);
            });
        }

        // Save results to file
        const resultsFile = path.join(dataDir, `integration-results-${Date.now()}.json`);
        fs.writeFileSync(resultsFile, JSON.stringify({
            summary: {
                totalProcessed: this.processedContacts.length,
                created,
                updated,
                errors: this.errors.length,
                apiCalls: this.apiCallCount,
                elapsedTime: (Date.now() - this.startTime) / 1000
            },
            processedContacts: this.processedContacts,
            errors: this.errors
        }, null, 2));

        console.log(`💾 Results saved to: ${resultsFile}`);
    }
}

// Run integration if called directly
if (require.main === module) {
    const integration = new EncompassToGHLIntegration();
    
    // Get contact limit from command line argument or default to 10
    const contactLimit = parseInt(process.argv[2]) || 10;
    
    integration.runIntegration(contactLimit).then(result => {
        console.log('\n✅ Integration completed successfully!');
    }).catch(error => {
        console.error('❌ Integration failed:', error.message);
        process.exit(1);
    });
}

module.exports = EncompassToGHLIntegration;
