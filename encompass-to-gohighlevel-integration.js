const axios = require('axios');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

console.log('🚀 Encompass to GoHighLevel Integration');
console.log('='.repeat(60));
console.log('📋 Features:');
console.log('   ✅ Fetch borrower data from Encompass API');
console.log('   ✅ Push to GoHighLevel with custom fields');
console.log('   ✅ Use borrower email and phone as primary identifiers');
console.log('   ✅ Map all available data fields');
console.log('='.repeat(60));

// Configuration
const encompassBaseUrl = 'https://api.elliemae.com';
const ghlBaseUrl = 'https://services.leadconnectorhq.com';

// Encompass credentials
const encompassUsername = process.env.ENCOMPASS_USERNAME;
const encompassPassword = process.env.ENCOMPASS_PASSWORD;
const encompassClientId = process.env.ENCOMPASS_CLIENT_ID;
const encompassClientSecret = process.env.ENCOMPASS_CLIENT_SECRET;

// GoHighLevel credentials
const ghlApiKey = process.env.GOHIGHLEVEL_API_KEY;
const ghlLocationId = process.env.GOHIGHLEVEL_LOCATION_ID;
const ghlPipelineId = process.env.GOHIGHLEVEL_PIPELINE_ID;
const ghlPipelineStageId = process.env.GOHIGHLEVEL_PIPELINE_STAGE_ID;

// Ensure data directory exists
const dataDir = path.join(__dirname, 'data');
if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
}

class EncompassToGHLIntegration {
    constructor() {
        this.apiCallCount = 0;
        this.startTime = Date.now();
        this.processedContacts = [];
        this.errors = [];
    }

    // Get Encompass access token
    async getEncompassToken() {
        try {
            console.log('🔐 Getting Encompass access token...');

            const tokenResponse = await axios.post(`${encompassBaseUrl}/oauth2/v1/token`,
                `grant_type=password&username=${encodeURIComponent(encompassUsername)}&password=${encodeURIComponent(encompassPassword)}`,
                {
                    headers: {
                        'Authorization': `Basic ${Buffer.from(`${encompassClientId}:${encompassClientSecret}`).toString('base64')}`,
                        'Content-Type': 'application/x-www-form-urlencoded'
                    }
                }
            );

            console.log('✅ Encompass access token obtained');
            return tokenResponse.data.access_token;
        } catch (error) {
            console.error('❌ Error getting Encompass token:', error.response?.data || error.message);
            throw error;
        }
    }

    // API call wrapper with rate limiting
    async makeApiCall(url, options, description) {
        this.apiCallCount++;
        console.log(`🌐 API Call ${this.apiCallCount}: ${description}`);
        
        try {
            const response = await axios(url, options);
            await new Promise(resolve => setTimeout(resolve, 200)); // Rate limiting
            return response.data;
        } catch (error) {
            console.error(`❌ ${description} failed:`, error.response?.data || error.message);
            this.errors.push({ description, error: error.response?.data || error.message });
            return null;
        }
    }

    // Get borrower contact IDs from Encompass
    async getEncompassContactIds(token, limit = 10) {
        const data = await this.makeApiCall(
            `${encompassBaseUrl}/encompass/v1/borrowerContactSelector`,
            {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                data: { start: 1, limit: limit }
            },
            `Getting ${limit} Encompass borrower contact IDs`
        );
        return data || [];
    }

    // Get detailed borrower contact information from Encompass
    async getEncompassContactDetails(contactId, token) {
        return await this.makeApiCall(
            `${encompassBaseUrl}/encompass/v1/borrowerContacts/${contactId}`,
            {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            },
            `Getting Encompass contact details for ${contactId}`
        );
    }

    // Transform Encompass data to GoHighLevel format
    transformToGHLFormat(encompassContact) {
        const phones = [];
        if (encompassContact.homePhone) phones.push(encompassContact.homePhone);
        if (encompassContact.workPhone) phones.push(encompassContact.workPhone);
        if (encompassContact.mobilePhone) phones.push(encompassContact.mobilePhone);

        // Primary phone (first available)
        const primaryPhone = phones[0] || '';

        // Full address
        const address = encompassContact.currentMailingAddress;
        const fullAddress = address ?
            `${address.street1 || ''} ${address.street2 || ''}, ${address.city || ''}, ${address.state || ''} ${address.zip || ''}`.trim() : '';

        return {
            // Standard GoHighLevel fields
            firstName: encompassContact.firstName || '',
            lastName: encompassContact.lastName || '',
            name: `${encompassContact.firstName || ''} ${encompassContact.lastName || ''}`.trim(),
            email: encompassContact.personalEmail || encompassContact.businessEmail || '',
            phone: primaryPhone,
            address1: address?.street1 || '',
            city: address?.city || '',
            state: address?.state || '',
            postalCode: address?.zip || '',
            country: 'US', // Default to US
            source: 'Encompass API',

            // Custom fields for additional borrower data
            customFields: [
                { key: 'encompass_contact_id', field_value: encompassContact.id || '' },
                { key: 'date_of_birth', field_value: encompassContact.birthdate || '' },
                { key: 'home_phone', field_value: encompassContact.homePhone || '' },
                { key: 'work_phone', field_value: encompassContact.workPhone || '' },
                { key: 'mobile_phone', field_value: encompassContact.mobilePhone || '' },
                { key: 'business_email', field_value: encompassContact.businessEmail || '' },
                { key: 'personal_email', field_value: encompassContact.personalEmail || '' },
                { key: 'full_address', field_value: fullAddress },
                { key: 'realtor_name', field_value: encompassContact.referral || '' },
                { key: 'import_date', field_value: new Date().toISOString() },
                { key: 'borrower_type', field_value: 'Primary Borrower' },

                // Placeholder fields for loan data (when API access is available)
                { key: 'interest_rate', field_value: '' },
                { key: 'closing_date', field_value: '' },
                { key: 'loan_originator', field_value: '' },
                { key: 'realtor_phone', field_value: '' },
                { key: 'realtor_email', field_value: '' },
                { key: 'property_address', field_value: '' },
                { key: 'loan_amount', field_value: '' },
                { key: 'loan_type', field_value: '' }
            ].filter(field => field.field_value !== ''), // Remove empty custom fields

            // Tags for organization and tracking
            tags: ['Encompass Import', 'Mortgage Lead', 'Borrower', 'Real Estate']
        };
    }

    // Function to search for contacts in GoHighLevel
    async searchGoHighLevelContact(searchParams) {
        console.log(`🔍 Searching for contact in GoHighLevel with params:`, JSON.stringify(searchParams));

        const url = `${ghlBaseUrl}/contacts/search`;
        const headers = {
            'Accept': 'application/json',
            'Authorization': `Bearer ${ghlApiKey}`,
            'Content-Type': 'application/json',
            'Version': '2021-07-28'
        };

        // Prepare search request body
        const requestBody = {
            locationId: ghlLocationId,
            pageLimit: 10,
            filters: []
        };

        // Add filters based on provided search parameters
        if (searchParams.firstName) {
            const trimmedFirstName = searchParams.firstName.trim();
            requestBody.filters.push({
                field: 'firstNameLowerCase',
                operator: 'contains',
                value: trimmedFirstName.toLowerCase()
            });
        }

        if (searchParams.lastName) {
            const trimmedLastName = searchParams.lastName.trim();
            requestBody.filters.push({
                field: 'lastNameLowerCase',
                operator: 'contains',
                value: trimmedLastName.toLowerCase()
            });
        }

        if (searchParams.email) {
            requestBody.filters.push({
                field: 'email',
                operator: 'eq',
                value: searchParams.email
            });
        }

        if (searchParams.phone) {
            // Remove any non-digit characters from phone number for comparison
            const cleanPhone = searchParams.phone.replace(/\D/g, '');
            if (cleanPhone) {
                requestBody.filters.push({
                    field: 'phone',
                    operator: 'contains',
                    value: cleanPhone
                });
            }
        }

        try {
            const response = await axios.post(url, requestBody, { headers });
            await new Promise(resolve => setTimeout(resolve, 200)); // Rate limiting

            if (response.data && response.data.contacts && response.data.contacts.length > 0) {
                console.log(`✅ Found ${response.data.contacts.length} matching contacts in GoHighLevel`);
                return response.data.contacts[0];
            }

            console.log(`ℹ️ No matching contacts found in GoHighLevel`);
            return null;
        } catch (error) {
            console.error(`❌ Error searching for contact in GoHighLevel:`, error.response?.data || error.message);
            return null;
        }
    }

    // Function to check for duplicate contacts using the dedicated API
    async checkDuplicateContact(contactData) {
        // Trim whitespace from names
        const trimmedFirstName = contactData.firstName ? contactData.firstName.trim() : '';
        const trimmedLastName = contactData.lastName ? contactData.lastName.trim() : '';

        console.log(`🔍 Checking for duplicate contact using dedicated API for "${trimmedFirstName}" "${trimmedLastName}"`);

        const baseUrl = `${ghlBaseUrl}/contacts/search/duplicate?locationId=${ghlLocationId}`;
        const headers = {
            'Accept': 'application/json',
            'Authorization': `Bearer ${ghlApiKey}`,
            'Version': '2021-07-28'
        };

        try {
            // Only try with email if it exists and is not empty
            if (contactData.email && contactData.email.trim()) {
                // Properly encode email, ensuring + and @ are correctly encoded
                const encodedEmail = encodeURIComponent(contactData.email.trim())
                    .replace(/\+/g, '%2B')
                    .replace(/@/g, '%40');

                const emailUrl = `${baseUrl}&email=${encodedEmail}`;
                console.log(`   📧 Checking duplicate by email: ${contactData.email}`);

                const emailResponse = await axios.get(emailUrl, { headers });
                await new Promise(resolve => setTimeout(resolve, 200)); // Rate limiting

                if (emailResponse.data && emailResponse.data.contact && emailResponse.data.contact.id) {
                    console.log(`   ✅ Found duplicate contact by email with ID: ${emailResponse.data.contact.id}`);
                    return emailResponse.data.contact.id;
                }
            }

            // Only try with phone if it exists, is not empty, and contains digits
            if (contactData.phone && contactData.phone.trim() && /\d/.test(contactData.phone)) {
                // Format phone number to include + prefix if not present
                let formattedPhone = contactData.phone.replace(/\D/g, '');
                if (!formattedPhone.startsWith('+')) {
                    formattedPhone = '+' + formattedPhone;
                }

                // Properly encode phone number, ensuring + is correctly encoded as %2B
                const encodedPhone = formattedPhone.replace(/\+/g, '%2B');

                const phoneUrl = `${baseUrl}&phone=${encodedPhone}`;
                console.log(`   📞 Checking duplicate by phone: ${contactData.phone}`);

                const phoneResponse = await axios.get(phoneUrl, { headers });
                await new Promise(resolve => setTimeout(resolve, 200)); // Rate limiting

                if (phoneResponse.data && phoneResponse.data.contact && phoneResponse.data.contact.id) {
                    console.log(`   ✅ Found duplicate contact by phone with ID: ${phoneResponse.data.contact.id}`);
                    return phoneResponse.data.contact.id;
                }
            }

            console.log(`   ℹ️ No duplicate contacts found using dedicated API`);
            return null;
        } catch (error) {
            console.error(`   ❌ Error checking for duplicate contact:`, error.response?.data || error.message);
            return null;
        }
    }

    // Create or update contact in GoHighLevel
    async createOrUpdateGHLContact(contactData) {
        try {
            // First, try to find existing contact using the dedicated duplicate check API
            const existingContactId = await this.checkDuplicateContact(contactData);

            // Prepare contact data for GoHighLevel
            const ghlContactData = {
                locationId: ghlLocationId,
                ...contactData
            };

            if (existingContactId) {
                // Update existing contact - remove locationId for updates
                const updateData = { ...contactData };
                delete updateData.locationId;

                const updatedContact = await this.makeApiCall(
                    `${ghlBaseUrl}/contacts/${existingContactId}`,
                    {
                        method: 'PUT',
                        headers: {
                            'Authorization': `Bearer ${ghlApiKey}`,
                            'Content-Type': 'application/json',
                            'Version': '2021-07-28'
                        },
                        data: updateData
                    },
                    `Updating existing GHL contact: ${contactData.firstName} ${contactData.lastName}`
                );

                return { action: 'updated', contact: updatedContact, contactId: existingContactId };
            } else {
                // Create new contact - include locationId for creation
                const newContact = await this.makeApiCall(
                    `${ghlBaseUrl}/contacts/`,
                    {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${ghlApiKey}`,
                            'Content-Type': 'application/json',
                            'Version': '2021-07-28'
                        },
                        data: ghlContactData
                    },
                    `Creating new GHL contact: ${contactData.firstName} ${contactData.lastName}`
                );

                return { action: 'created', contact: newContact, contactId: newContact?.contact?.id };
            }

        } catch (error) {
            console.error(`❌ Error creating/updating GHL contact:`, error.response?.data || error.message);
            this.errors.push({
                description: `GHL contact operation for ${contactData.firstName} ${contactData.lastName}`,
                error: error.response?.data || error.message
            });
            return null;
        }
    }

    // Search for opportunities in GoHighLevel
    async searchGoHighLevelOpportunities(searchParams) {
        const url = `${ghlBaseUrl}/opportunities/search`;
        const headers = {
            'Accept': 'application/json',
            'Authorization': `Bearer ${ghlApiKey}`,
            'Content-Type': 'application/json',
            'Version': '2021-07-28'
        };

        const requestBody = {
            locationId: ghlLocationId,
            ...searchParams
        };

        try {
            const response = await axios.post(url, requestBody, { headers });
            await new Promise(resolve => setTimeout(resolve, 200)); // Rate limiting

            return {
                success: true,
                opportunities: response.data?.opportunities || []
            };
        } catch (error) {
            console.error(`❌ Error searching opportunities:`, error.response?.data || error.message);
            return { success: false, opportunities: [] };
        }
    }

    // Create opportunity in GoHighLevel
    async createOpportunity(contactId, contactData) {
        // Validate that we have a contactId before proceeding
        if (!contactId) {
            console.error(`❌ Cannot create opportunity: Missing contactId`);
            return null;
        }

        console.log(`🎯 Creating opportunity for contact ID: ${contactId}`);

        try {
            // First, check if an opportunity already exists for this contact
            const existingOpportunities = await this.searchGoHighLevelOpportunities({
                contact_id: contactId,
                pipeline_id: ghlPipelineId,
                status: 'open'
            });

            // If we found existing opportunities for this contact in this pipeline
            if (existingOpportunities.success && existingOpportunities.opportunities && existingOpportunities.opportunities.length > 0) {
                const existingOpp = existingOpportunities.opportunities[0];
                console.log(`   ✅ Found existing opportunity: ID ${existingOpp.id}, status: ${existingOpp.status}`);
                return {
                    success: true,
                    opportunityId: existingOpp.id,
                    isNew: false,
                    message: 'Existing opportunity found'
                };
            }

            // If no existing opportunity, create a new one
            console.log(`   🆕 Creating new opportunity for contact ID: ${contactId}`);

            const opportunityData = {
                pipelineId: ghlPipelineId,
                pipelineStageId: ghlPipelineStageId,
                locationId: ghlLocationId,
                name: `${contactData.firstName || ''} ${contactData.lastName || ''} - Mortgage Lead`.trim(),
                status: "open",
                contactId,
                source: 'Encompass API'
            };

            const response = await this.makeApiCall(
                `${ghlBaseUrl}/opportunities/`,
                {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${ghlApiKey}`,
                        'Content-Type': 'application/json',
                        'Version': '2021-07-28'
                    },
                    data: opportunityData
                },
                `Creating opportunity for ${contactData.firstName} ${contactData.lastName}`
            );

            const newOpportunityId = response?.opportunity?.id;
            console.log(`   ✅ Created new opportunity with ID: ${newOpportunityId}`);

            return {
                success: true,
                opportunityId: newOpportunityId,
                isNew: true,
                message: 'New opportunity created'
            };

        } catch (error) {
            const errorData = error.response?.data;
            console.error(`❌ Failed to create opportunity:`, errorData || error.message);

            // Check if this is a duplicate opportunity error
            if (errorData?.statusCode === 400 && errorData?.message?.includes('duplicate')) {
                console.log(`   ℹ️ Duplicate opportunity detected, searching for existing one`);

                try {
                    const duplicateSearch = await this.searchGoHighLevelOpportunities({
                        contact_id: contactId,
                        pipeline_id: ghlPipelineId
                    });

                    if (duplicateSearch.success && duplicateSearch.opportunities && duplicateSearch.opportunities.length > 0) {
                        const existingOpp = duplicateSearch.opportunities[0];
                        console.log(`   ✅ Found duplicate opportunity: ID ${existingOpp.id}`);

                        return {
                            success: true,
                            opportunityId: existingOpp.id,
                            isNew: false,
                            message: 'Duplicate opportunity found'
                        };
                    }
                } catch (searchError) {
                    console.error(`❌ Error searching for duplicate opportunity:`, searchError.message);
                }
            }

            this.errors.push({
                description: `Opportunity creation for ${contactData.firstName} ${contactData.lastName}`,
                error: errorData || error.message
            });
            return null;
        }
    }

    // Main integration function
    async runIntegration(contactLimit = 10) {
        console.log(`🚀 Starting Encompass to GoHighLevel integration for ${contactLimit} contacts...\n`);

        try {
            // Step 1: Get Encompass data
            console.log('📥 Step 1: Fetching data from Encompass...');
            const encompassToken = await this.getEncompassToken();
            
            const contactIds = await this.getEncompassContactIds(encompassToken, contactLimit);
            console.log(`✅ Retrieved ${contactIds.length} contact IDs from Encompass`);

            // Step 2: Process each contact
            console.log('\n🔄 Step 2: Processing and pushing to GoHighLevel...');
            
            for (let i = 0; i < Math.min(contactIds.length, contactLimit); i++) {
                const contactId = contactIds[i].id;
                console.log(`\n📄 Processing contact ${i + 1}/${Math.min(contactIds.length, contactLimit)}: ${contactId}`);

                // Get detailed contact info from Encompass
                const encompassContact = await this.getEncompassContactDetails(contactId, encompassToken);
                if (!encompassContact) {
                    console.log(`   ❌ Failed to get Encompass contact details`);
                    continue;
                }

                console.log(`   ✅ Encompass: ${encompassContact.firstName || ''} ${encompassContact.lastName || ''}`);
                console.log(`   📧 Email: ${encompassContact.personalEmail || encompassContact.businessEmail || 'N/A'}`);
                console.log(`   📞 Phone: ${encompassContact.homePhone || encompassContact.workPhone || encompassContact.mobilePhone || 'N/A'}`);

                // Transform to GoHighLevel format
                const ghlContactData = this.transformToGHLFormat(encompassContact);

                // Push to GoHighLevel
                const ghlResult = await this.createOrUpdateGHLContact(ghlContactData);

                if (ghlResult) {
                    console.log(`   ✅ GoHighLevel: Contact ${ghlResult.action} successfully`);
                    console.log(`   🆔 GHL Contact ID: ${ghlResult.contactId || 'N/A'}`);

                    // Create opportunity if we have pipeline configuration
                    let opportunityResult = null;
                    if (ghlPipelineId && ghlPipelineStageId && ghlResult.contactId) {
                        console.log(`   🎯 Creating opportunity for contact...`);
                        opportunityResult = await this.createOpportunity(ghlResult.contactId, ghlContactData);

                        if (opportunityResult && opportunityResult.success) {
                            console.log(`   ✅ Opportunity ${opportunityResult.isNew ? 'created' : 'found'}: ${opportunityResult.opportunityId}`);
                        } else {
                            console.log(`   ⚠️ Opportunity creation skipped or failed`);
                        }
                    } else {
                        console.log(`   ℹ️ Opportunity creation skipped (missing pipeline configuration)`);
                    }

                    this.processedContacts.push({
                        encompassId: contactId,
                        ghlContactId: ghlResult.contactId,
                        ghlOpportunityId: opportunityResult?.opportunityId || null,
                        action: ghlResult.action,
                        opportunityAction: opportunityResult?.isNew ? 'created' : (opportunityResult?.success ? 'found' : 'failed'),
                        name: `${encompassContact.firstName || ''} ${encompassContact.lastName || ''}`.trim(),
                        email: encompassContact.personalEmail || encompassContact.businessEmail,
                        phone: encompassContact.homePhone || encompassContact.workPhone || encompassContact.mobilePhone
                    });
                } else {
                    console.log(`   ❌ Failed to create/update in GoHighLevel`);
                }
            }

            // Step 3: Summary and results
            console.log('\n📊 Step 3: Integration Summary...');
            this.generateSummary();

            return {
                processedContacts: this.processedContacts,
                errors: this.errors,
                apiCallCount: this.apiCallCount,
                elapsedTime: (Date.now() - this.startTime) / 1000
            };

        } catch (error) {
            console.error('❌ Integration failed:', error.message);
            throw error;
        }
    }

    // Generate integration summary
    generateSummary() {
        const created = this.processedContacts.filter(c => c.action === 'created').length;
        const updated = this.processedContacts.filter(c => c.action === 'updated').length;
        const opportunitiesCreated = this.processedContacts.filter(c => c.opportunityAction === 'created').length;
        const opportunitiesFound = this.processedContacts.filter(c => c.opportunityAction === 'found').length;
        const opportunitiesFailed = this.processedContacts.filter(c => c.opportunityAction === 'failed').length;

        console.log('🎉 Integration completed!');
        console.log('='.repeat(60));
        console.log(`📥 Total contacts processed: ${this.processedContacts.length}`);
        console.log(`✨ New contacts created: ${created}`);
        console.log(`🔄 Existing contacts updated: ${updated}`);
        console.log(`🎯 New opportunities created: ${opportunitiesCreated}`);
        console.log(`🔍 Existing opportunities found: ${opportunitiesFound}`);
        console.log(`⚠️ Opportunity failures: ${opportunitiesFailed}`);
        console.log(`❌ Errors encountered: ${this.errors.length}`);
        console.log(`🌐 Total API calls made: ${this.apiCallCount}`);
        console.log(`⏱️ Total time: ${((Date.now() - this.startTime) / 1000).toFixed(2)} seconds`);
        console.log('='.repeat(60));

        if (this.errors.length > 0) {
            console.log('\n⚠️ Errors encountered:');
            this.errors.forEach((error, index) => {
                console.log(`   ${index + 1}. ${error.description}: ${JSON.stringify(error.error)}`);
            });
        }

        // Save results to file
        const resultsFile = path.join(dataDir, `integration-results-${Date.now()}.json`);
        fs.writeFileSync(resultsFile, JSON.stringify({
            summary: {
                totalProcessed: this.processedContacts.length,
                contactsCreated: created,
                contactsUpdated: updated,
                opportunitiesCreated,
                opportunitiesFound,
                opportunitiesFailed,
                errors: this.errors.length,
                apiCalls: this.apiCallCount,
                elapsedTime: (Date.now() - this.startTime) / 1000
            },
            processedContacts: this.processedContacts,
            errors: this.errors
        }, null, 2));

        console.log(`💾 Results saved to: ${resultsFile}`);
    }
}

// Run integration if called directly
if (require.main === module) {
    const integration = new EncompassToGHLIntegration();
    
    // Get contact limit from command line argument or default to 10
    const contactLimit = parseInt(process.argv[2]) || 10;
    
    integration.runIntegration(contactLimit).then(result => {
        console.log('\n✅ Integration completed successfully!');
    }).catch(error => {
        console.error('❌ Integration failed:', error.message);
        process.exit(1);
    });
}

module.exports = EncompassToGHLIntegration;
