const EncompassApi = require('./encompassApi');
const axios = require('axios');
const logger = require('./logger');
const fs = require('fs');
const path = require('path');

async function testUsersAndLeads() {
  console.log('👥 Testing Encompass Users and Lead Data Access...\n');

  try {
    // Initialize Encompass API
    const encompassApi = new EncompassApi();
    
    console.log('1. Authenticating with Encompass API...');
    const authResult = await encompassApi.authenticate();
    if (!authResult) {
      throw new Error('Failed to authenticate with Encompass API');
    }
    console.log('✅ Successfully authenticated\n');

    const baseUrl = 'https://api.elliemae.com';
    const headers = {
      'Authorization': `Bearer ${encompassApi.accessToken}`,
      'Content-Type': 'application/json'
    };

    console.log('2. Fetching all users in the instance...');
    
    try {
      // Get all users
      const usersResponse = await axios.get(`${baseUrl}/encompass/v1/users`, {
        headers: headers,
        params: {
          limit: 100 // Get up to 100 users
        }
      });

      const users = usersResponse.data;
      console.log(`✅ Found ${users.length} users in instance BE11140034\n`);

      console.log('📋 User List:');
      console.log('=============');
      users.forEach((user, index) => {
        console.log(`${index + 1}. ${user.displayName || user.firstName + ' ' + user.lastName} (${user.id})`);
        console.log(`   Email: ${user.email || 'N/A'}`);
        console.log(`   Role: ${user.personas?.[0]?.displayName || 'N/A'}`);
        console.log(`   Active: ${user.isActive ? 'Yes' : 'No'}`);
        console.log('');
      });

      console.log('3. Testing lead/loan access for each user...\n');

      const leadResults = [];
      
      for (const user of users.slice(0, 5)) { // Test first 5 users to avoid rate limits
        console.log(`Testing loan access for: ${user.displayName || user.id}`);
        
        try {
          // Try to get loans for this user
          const loansResponse = await axios.get(`${baseUrl}/encompass/v1/loans`, {
            headers: headers,
            params: {
              filter: `fields(1109,4000,4002,4008)`, // Basic fields
              limit: 10,
              // Try filtering by loan officer or processor
              'loanOfficer.id': user.id
            }
          });

          const loans = loansResponse.data;
          console.log(`   ✅ Found ${loans.length} loans for ${user.displayName || user.id}`);
          
          leadResults.push({
            user: user,
            loanCount: loans.length,
            loans: loans,
            status: 'SUCCESS'
          });

        } catch (error) {
          const errorMsg = error.response?.data?.summary || error.message;
          console.log(`   ❌ No access or error for ${user.displayName || user.id}: ${errorMsg}`);
          
          leadResults.push({
            user: user,
            loanCount: 0,
            loans: [],
            status: 'FAILED',
            error: errorMsg
          });
        }
      }

      console.log('\n4. Alternative: Try getting all loans without user filter...');
      
      try {
        // Try different approaches to get loan data
        const approaches = [
          {
            name: 'Recent Loans (Last 7 days)',
            params: {
              filter: 'fields(1109,4000,4002,4008,4009,4010,4011)',
              limit: 20
            }
          },
          {
            name: 'All Loans (Limited fields)',
            params: {
              filter: 'fields(1109,4000,4002)',
              limit: 50
            }
          },
          {
            name: 'Pipeline Loans',
            params: {
              filter: 'fields(1109,4000,4002,4008,4009)',
              limit: 30
            }
          }
        ];

        for (const approach of approaches) {
          try {
            console.log(`\nTrying: ${approach.name}`);
            
            const response = await axios.get(`${baseUrl}/encompass/v1/loans`, {
              headers: headers,
              params: approach.params
            });

            const loans = response.data;
            console.log(`✅ ${approach.name}: Found ${loans.length} loans`);
            
            if (loans.length > 0) {
              console.log('📊 Sample loan data structure:');
              const sampleLoan = loans[0];
              console.log('   Available fields:', Object.keys(sampleLoan));
              
              // Show field mappings
              console.log('   Field values:');
              Object.entries(sampleLoan).forEach(([key, value]) => {
                if (value !== null && value !== undefined) {
                  const displayValue = typeof value === 'string' ? value.substring(0, 50) : value;
                  console.log(`     ${key}: ${displayValue}`);
                }
              });

              // Save sample data
              const dataDir = path.join(__dirname, 'data');
              if (!fs.existsSync(dataDir)) {
                fs.mkdirSync(dataDir, { recursive: true });
              }

              const sampleFile = path.join(dataDir, `encompass-loans-${approach.name.toLowerCase().replace(/\s+/g, '-')}.json`);
              fs.writeFileSync(sampleFile, JSON.stringify({
                timestamp: new Date().toISOString(),
                approach: approach.name,
                totalLoans: loans.length,
                sampleLoans: loans.slice(0, 3), // Save first 3 loans
                fieldAnalysis: analyzeFields(loans)
              }, null, 2));

              console.log(`   💾 Sample data saved to: ${sampleFile}`);
              break; // Stop after first successful approach
            }

          } catch (error) {
            const errorMsg = error.response?.data?.summary || error.message;
            console.log(`❌ ${approach.name}: ${errorMsg}`);
          }
        }

      } catch (error) {
        console.log(`❌ All loan access attempts failed: ${error.message}`);
      }

      console.log('\n5. Summary and Recommendations:');
      console.log('===============================');
      
      const successfulUsers = leadResults.filter(r => r.status === 'SUCCESS' && r.loanCount > 0);
      
      if (successfulUsers.length > 0) {
        console.log(`✅ Found loan data for ${successfulUsers.length} users:`);
        successfulUsers.forEach(result => {
          console.log(`   - ${result.user.displayName || result.user.id}: ${result.loanCount} loans`);
        });
        console.log('\n🎉 Integration should work! You have access to loan data.');
      } else {
        console.log('⚠️  No loan data found for individual users.');
        console.log('   This could mean:');
        console.log('   1. No recent loan applications');
        console.log('   2. Loans are assigned differently');
        console.log('   3. Need different API permissions');
        console.log('   4. Need to use different endpoints');
      }

      console.log('\n📋 Next Steps:');
      console.log('1. Review the saved sample data files');
      console.log('2. Check if any approach successfully retrieved loans');
      console.log('3. If successful, update the integration to use the working approach');
      console.log('4. If no success, contact Encompass admin for loan data access permissions');

    } catch (error) {
      console.error('❌ Failed to fetch users:', error.response?.data || error.message);
      
      // Try alternative user endpoints
      console.log('\nTrying alternative user endpoints...');
      
      try {
        const currentUserResponse = await axios.get(`${baseUrl}/encompass/v1/users/current`, {
          headers: headers
        });
        
        console.log('✅ Current user info retrieved:');
        console.log(JSON.stringify(currentUserResponse.data, null, 2));
        
      } catch (altError) {
        console.error('❌ Alternative user endpoint also failed:', altError.response?.data || altError.message);
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

function analyzeFields(loans) {
  const fieldStats = {};
  
  loans.forEach(loan => {
    Object.entries(loan).forEach(([field, value]) => {
      if (!fieldStats[field]) {
        fieldStats[field] = {
          count: 0,
          sampleValues: [],
          dataTypes: new Set()
        };
      }
      
      if (value !== null && value !== undefined && value !== '') {
        fieldStats[field].count++;
        fieldStats[field].dataTypes.add(typeof value);
        
        if (fieldStats[field].sampleValues.length < 3) {
          fieldStats[field].sampleValues.push(String(value).substring(0, 50));
        }
      }
    });
  });
  
  // Convert Set to Array for JSON serialization
  Object.values(fieldStats).forEach(stats => {
    stats.dataTypes = [...stats.dataTypes];
  });
  
  return fieldStats;
}

// Run test if this file is executed directly
if (require.main === module) {
  testUsersAndLeads()
    .then(() => {
      console.log('\n✅ Users and leads test completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testUsersAndLeads };
