const axios = require('axios');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

console.log('🧪 Encompass Contact Fetcher - 100 Full Contact Details');
console.log('='.repeat(60));

// Configuration
const baseUrl = 'https://api.elliemae.com';
const username = process.env.ENCOMPASS_USERNAME;
const password = process.env.ENCOMPASS_PASSWORD;
const clientId = process.env.ENCOMPASS_CLIENT_ID;
const clientSecret = process.env.ENCOMPASS_CLIENT_SECRET;

// Ensure data directory exists
const dataDir = path.join(__dirname, 'data');
if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
}

// Authentication function (borrowed from working files)
async function getAccessToken() {
    try {
        console.log('🔐 Getting access token...');
        
        const tokenResponse = await axios.post(`${baseUrl}/oauth2/v1/token`, 
            `grant_type=password&username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`,
            {
                headers: {
                    'Authorization': `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`,
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            }
        );
        
        console.log('✅ Access token obtained successfully');
        return tokenResponse.data.access_token;
    } catch (error) {
        console.error('❌ Error getting access token:', error.response?.data || error.message);
        throw error;
    }
}

class FullContactFetcher {
    constructor() {
        this.fetchedContacts = [];
        this.startTime = Date.now();
    }

    // Get contact IDs (first 100)
    async getContactIds(limit = 100) {
        console.log(`\n📋 Getting first ${limit} contact IDs...`);
        
        try {
            const token = await getAccessToken();
            
            const response = await axios.post(
                `${baseUrl}/encompass/v1/borrowerContactSelector`,
                { start: 1, limit: limit },
                {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );

            let contacts = [];
            if (Array.isArray(response.data)) {
                contacts = response.data;
            }

            console.log(`✅ Retrieved ${contacts.length} contact IDs`);
            return contacts.map(contact => contact.id);

        } catch (error) {
            console.error('❌ Error getting contact IDs:', error.response?.data || error.message);
            throw error;
        }
    }

    // Get detailed contact information
    async getContactDetails(contactId, token) {
        try {
            const response = await axios.get(
                `${baseUrl}/encompass/v1/borrowerContacts/${contactId}`,
                {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );

            return response.data;
        } catch (error) {
            console.error(`❌ Error fetching details for contact ${contactId}:`, error.response?.data || error.message);
            return null;
        }
    }

    // Fetch full details for 100 contacts
    async fetch100FullContacts() {
        console.log('\n📥 Fetching 100 full contact details...');

        try {
            // Step 1: Get contact IDs
            const contactIds = await this.getContactIds(100);
            
            if (contactIds.length === 0) {
                console.log('❌ No contact IDs found');
                return [];
            }

            // Step 2: Get access token for detailed fetching
            const token = await getAccessToken();

            // Step 3: Fetch detailed information for each contact
            console.log(`\n🔍 Fetching detailed information for ${contactIds.length} contacts...`);
            const fullContacts = [];
            
            for (let i = 0; i < contactIds.length; i++) {
                const contactId = contactIds[i];
                console.log(`📄 Fetching contact ${i + 1}/${contactIds.length}: ${contactId}`);
                
                const contactDetails = await this.getContactDetails(contactId, token);
                
                if (contactDetails) {
                    fullContacts.push(contactDetails);
                    
                    // Log key information if available
                    const name = `${contactDetails.firstName || ''} ${contactDetails.lastName || ''}`.trim();
                    const email = contactDetails.personalEmail || contactDetails.workEmail || 'No email';
                    const phone = contactDetails.homePhone || contactDetails.workPhone || contactDetails.mobilePhone || 'No phone';
                    
                    console.log(`   ✅ ${name || 'No name'} | ${email} | ${phone}`);
                } else {
                    console.log(`   ❌ Failed to fetch details for ${contactId}`);
                }

                // Rate limiting to avoid overwhelming the API
                await new Promise(resolve => setTimeout(resolve, 200));
            }

            console.log(`\n✅ Successfully fetched ${fullContacts.length} full contact details`);
            this.fetchedContacts = fullContacts;
            return fullContacts;

        } catch (error) {
            console.error('❌ Error fetching full contacts:', error.response?.data || error.message);
            throw error;
        }
    }

    // Save contacts to JSON file
    saveContactsToFile(contacts, filename) {
        try {
            const filepath = path.join(dataDir, filename);
            const dataToSave = {
                metadata: {
                    totalContacts: contacts.length,
                    fetchedAt: new Date().toISOString(),
                    elapsedTime: (Date.now() - this.startTime) / 1000,
                    source: 'Encompass API - Full Contact Details'
                },
                contacts: contacts
            };

            fs.writeFileSync(filepath, JSON.stringify(dataToSave, null, 2));
            console.log(`💾 Contacts saved to: ${filename}`);
            console.log(`📁 Full path: ${filepath}`);
            return filepath;
        } catch (error) {
            console.error('❌ Error saving contacts:', error.message);
            return null;
        }
    }

    // Analyze contact data
    analyzeContactData(contacts) {
        const analysis = {
            totalContacts: contacts.length,
            fieldsAnalysis: {},
            withEmail: 0,
            withPhone: 0,
            withAddress: 0,
            withName: 0,
            withBirthDate: 0,
            withRealtor: 0
        };

        // Analyze each contact
        contacts.forEach(contact => {
            // Count contacts with key information
            if (contact.personalEmail || contact.workEmail) analysis.withEmail++;
            if (contact.homePhone || contact.workPhone || contact.mobilePhone) analysis.withPhone++;
            if (contact.currentMailingAddress || contact.currentResidenceAddress) analysis.withAddress++;
            if (contact.firstName || contact.lastName) analysis.withName++;
            if (contact.birthdate) analysis.withBirthDate++;
            if (contact.realtorName || contact.realtorEmail || contact.realtorPhone) analysis.withRealtor++;

            // Analyze all fields
            Object.keys(contact).forEach(field => {
                if (!analysis.fieldsAnalysis[field]) {
                    analysis.fieldsAnalysis[field] = 0;
                }
                if (contact[field] !== null && contact[field] !== undefined && contact[field] !== '') {
                    analysis.fieldsAnalysis[field]++;
                }
            });
        });

        return analysis;
    }

    // Main execution function
    async runFullContactFetch() {
        console.log('🚀 Starting Encompass full contact fetch...');

        try {
            // Fetch 100 full contacts
            const contacts = await this.fetch100FullContacts();

            if (contacts.length === 0) {
                console.log('❌ No contacts fetched');
                return;
            }

            // Save to JSON file
            const filename = `encompass-100-full-contacts-${Date.now()}.json`;
            const filepath = this.saveContactsToFile(contacts, filename);

            // Analyze the data
            const analysis = this.analyzeContactData(contacts);

            console.log('\n📊 Contact Data Analysis:');
            console.log(`   - Total contacts fetched: ${analysis.totalContacts}`);
            console.log(`   - Contacts with name: ${analysis.withName}`);
            console.log(`   - Contacts with email: ${analysis.withEmail}`);
            console.log(`   - Contacts with phone: ${analysis.withPhone}`);
            console.log(`   - Contacts with address: ${analysis.withAddress}`);
            console.log(`   - Contacts with birth date: ${analysis.withBirthDate}`);
            console.log(`   - Contacts with realtor info: ${analysis.withRealtor}`);

            // Show sample contact
            if (contacts.length > 0) {
                console.log('\n📋 Sample Contact:');
                console.log(JSON.stringify(contacts[0], null, 2));
            }

            console.log('\n🎉 Process completed successfully!');
            console.log('='.repeat(60));
            console.log(`📥 Contacts fetched: ${contacts.length}`);
            console.log(`💾 Data saved to: ${filepath}`);
            console.log('='.repeat(60));

            return {
                fetchedContacts: contacts.length,
                savedToFile: filepath,
                analysis: analysis
            };

        } catch (error) {
            console.error('❌ Process failed:', error.message);
            throw error;
        }
    }
}

// Run the fetcher
async function main() {
    const fetcher = new FullContactFetcher();
    try {
        const result = await fetcher.runFullContactFetch();
        console.log('\n✅ Contact fetch completed successfully!');
        console.log('📊 Final Results:', JSON.stringify(result, null, 2));
    } catch (error) {
        console.error('❌ Contact fetch failed:', error.message);
        process.exit(1);
    }
}

// Execute if run directly
if (require.main === module) {
    main();
}

module.exports = FullContactFetcher;
