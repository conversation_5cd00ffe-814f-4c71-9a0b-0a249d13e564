# Encompass External User Webhook Implementation

This document describes the implementation of real-time borrower detection using Encompass External User webhooks.

## 🎯 **Overview**

When a new borrower (External User) is created in Encompass, a webhook notification is automatically sent to our integration server, which then:

1. **Receives the webhook** notification
2. **Extracts the user ID** from the payload
3. **Fetches comprehensive borrower data** from Encompass APIs
4. **Saves the data** to JSON files for processing
5. **Prepares for GoHighLevel sync** (future enhancement)

## 🔧 **Technical Implementation**

### **Webhook Endpoint**
```
POST /webhook/external-users
```

### **Supported Events**
- **External User Create**: When a new borrower is added to Encompass
- **External User Update**: When borrower information is modified
- **External User Delete**: When a borrower is removed

### **Webhook Payload Structure**
Based on Encompass API documentation:

```json
{
  "eventId": "b7cb3e9c-2481-4c9c-af90-55a5d1b6a693",
  "eventTime": "2025-07-16T21:00:00.000Z",
  "eventType": "create",
  "meta": {
    "userId": "admin",
    "resourceType": "ExternalUsers",
    "resourceId": "423253b1-cd23-4424-b6e8-204dfce2751e",
    "instanceId": "**********",
    "resourceRef": "/encompass/v3/externalUsers",
    "payload": {
      "entities": [
        {
          "id": "test-borrower-001"
        }
      ]
    }
  }
}
```

## 📋 **Data Processing Flow**

### **1. Webhook Reception**
```javascript
async handleExternalUserWebhook(req, res) {
    // Parse JSON payload (handles Buffer/String/Object)
    // Validate event type and resource type
    // Extract user ID from payload
    // Process asynchronously
}
```

### **2. Borrower Data Extraction**
```javascript
async processNewBorrower(userId) {
    // Search for borrower contact using user ID
    // Fetch comprehensive borrower data
    // Extract loan information (if available)
    // Get realtor and loan originator details
    // Save to JSON file
}
```

### **3. Comprehensive Data Structure**
The system extracts and structures the following data:

```json
{
  "borrower": {
    "id": "user-id",
    "name": "John Doe",
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "phones": {
      "home": "555-1234",
      "work": "555-5678",
      "mobile": "555-9012"
    },
    "dateOfBirth": "1980-01-01",
    "address": {...},
    "employer": {...}
  },
  "loan": {
    "id": "loan-number",
    "interestRate": 3.5,
    "closingDate": "2025-08-15",
    "propertyAddress": {...},
    "amount": 350000,
    "type": "Purchase"
  },
  "realtor": {
    "name": "Jane Smith",
    "phone": "555-2468",
    "email": "<EMAIL>"
  },
  "metadata": {
    "extractedAt": "2025-07-16T23:25:45.000Z",
    "source": "Encompass API",
    "version": "1.0"
  }
}
```

## 🚀 **Setup and Configuration**

### **1. Environment Variables**
```env
ENCOMPASS_API_URL=https://api.elliemae.com
ENCOMPASS_CLIENT_ID=your_client_id
ENCOMPASS_CLIENT_SECRET=your_client_secret
ENCOMPASS_USERNAME=your_username@encompass:instance_id
ENCOMPASS_PASSWORD=your_password
ENCOMPASS_WEBHOOK_SIGNING_KEY=optional_signing_key
```

### **2. Webhook Subscription**
To set up the webhook subscription in Encompass:

```bash
# List available webhook resources
npm run setup-webhook resources

# Create External User webhook subscription
npm run setup-webhook create
```

### **3. Testing**
```bash
# Test the webhook endpoint
npm run test-webhook

# Start the server with auto-reload
npm run dev
```

## 📊 **Monitoring and Logging**

### **Server Statistics**
The server tracks webhook processing statistics:
- `webhookEvents`: Total webhook events received
- `borrowersProcessed`: Number of borrowers successfully processed
- `errors`: Number of processing errors

### **Log Output Example**
```
[2025-07-16T23:25:41.970Z] 🔔 External User Webhook received
[2025-07-16T23:25:41.971Z] 👤 New External User created: 423253b1-cd23-4424-b6e8-204dfce2751e
[2025-07-16T23:25:41.972Z] 🔍 Processing new borrower with ID: test-borrower-001
[2025-07-16T23:25:41.973Z] 🚀 Starting comprehensive borrower data extraction
[2025-07-16T23:25:45.135Z] ✅ Retrieved 0 borrower contacts out of 0 total
```

## 📁 **File Storage**

### **Processed Data Location**
```
data/webhook-processed/
├── new-borrower-{userId}-{timestamp}.json
└── ...
```

### **File Naming Convention**
```
new-borrower-test-borrower-001-2025-07-16T23-25-45-000Z.json
```

## 🔄 **Integration Workflow**

### **Current Implementation**
1. ✅ Webhook reception and parsing
2. ✅ User ID extraction
3. ✅ Borrower data fetching
4. ✅ Comprehensive data structuring
5. ✅ JSON file storage
6. ✅ Error handling and logging

### **Future Enhancements**
1. 🔄 **GoHighLevel Sync**: Automatic contact creation
2. 🔄 **Duplicate Detection**: Check existing contacts
3. 🔄 **Opportunity Creation**: Create sales opportunities
4. 🔄 **Real-time Notifications**: Slack/email alerts
5. 🔄 **Data Validation**: Enhanced field validation

## 🛠 **API Endpoints**

### **Webhook Endpoints**
- `POST /webhook/external-users` - External User webhook handler
- `POST /webhook/encompass` - General Encompass webhook handler
- `POST /api/webhook/test` - Webhook connectivity test

### **Data Endpoints**
- `GET /api/borrowers` - Get all borrower contacts
- `GET /api/borrowers/count` - Get borrower count
- `GET /api/stats` - Server statistics

### **Management Endpoints**
- `GET /health` - Health check
- `GET /` - Dashboard
- `GET /api/configuration` - Configuration status

## 🔐 **Security Considerations**

### **Webhook Verification**
- Optional webhook signing key validation
- Request origin verification
- Payload integrity checks

### **API Security**
- OAuth2 token management
- Automatic token refresh
- Rate limiting compliance

## 📈 **Performance**

### **Processing Speed**
- Webhook acknowledgment: < 100ms
- Data extraction: 2-5 seconds
- File storage: < 50ms

### **Scalability**
- Asynchronous processing
- Non-blocking webhook responses
- Efficient API usage

## 🐛 **Troubleshooting**

### **Common Issues**
1. **No borrower contacts found**: Normal for test environments
2. **Authentication errors**: Check environment variables
3. **Webhook not received**: Verify subscription setup
4. **JSON parsing errors**: Check payload format

### **Debug Commands**
```bash
# Check server logs
npm run dev

# Test webhook manually
npm run test-webhook

# Verify configuration
curl http://localhost:3000/api/configuration
```

This implementation provides a robust foundation for real-time borrower processing and can be easily extended for additional features and integrations.
