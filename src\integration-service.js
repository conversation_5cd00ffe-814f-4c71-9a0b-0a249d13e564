#!/usr/bin/env node

/**
 * Enhanced Integration Service
 * Improved version of the original integration with better organization
 */

require('dotenv').config();
const BorrowerService = require('./services/borrower-service');
const GoHighLevelClient = require('./utils/gohighlevel-client');
const fs = require('fs');
const path = require('path');

class IntegrationService {
    constructor(config = {}) {
        this.borrowerService = new BorrowerService(config);
        this.ghlClient = new GoHighLevelClient(config);
        
        this.processedContacts = [];
        this.errors = [];
        this.startTime = Date.now();
        
        // Ensure data directory exists
        this.dataDir = path.join(__dirname, '../data');
        if (!fs.existsSync(this.dataDir)) {
            fs.mkdirSync(this.dataDir, { recursive: true });
        }
    }

    /**
     * Run the integration for a specified number of contacts
     * @param {number} contactLimit - Number of contacts to process
     */
    async runIntegration(contactLimit = 10) {
        console.log('🚀 Encompass to GoHighLevel Integration');
        console.log('='.repeat(60));
        console.log('📋 Features:');
        console.log('   ✅ Fetch borrower data from Encompass API');
        console.log('   ✅ Push to GoHighLevel with custom fields');
        console.log('   ✅ Use borrower email as primary identifier');
        console.log('   ✅ Create opportunities when configured');
        console.log('   ✅ Comprehensive data mapping');
        console.log('='.repeat(60));
        console.log(`🚀 Starting integration for ${contactLimit} contacts...\n`);

        try {
            // Step 1: Get borrower contact IDs
            console.log('📥 Step 1: Fetching data from Encompass...');
            const contactsResponse = await this.borrowerService.getBorrowerContactIds(contactLimit);
            console.log(`✅ Retrieved ${contactsResponse.contactIds.length} contact IDs from Encompass\n`);

            // Step 2: Process each contact
            console.log('🔄 Step 2: Processing and pushing to GoHighLevel...\n');
            
            for (let i = 0; i < contactsResponse.contactIds.length; i++) {
                const contactId = contactsResponse.contactIds[i];
                await this.processContact(contactId, i + 1, contactsResponse.contactIds.length);
            }

            // Step 3: Generate summary
            console.log('\n📊 Step 3: Integration Summary...');
            this.generateSummary();

            // Save results
            await this.saveResults();

            return {
                success: true,
                processed: this.processedContacts.length,
                errors: this.errors.length
            };

        } catch (error) {
            console.error('❌ Integration failed:', error.message);
            throw error;
        }
    }

    /**
     * Process a single contact
     * @param {string} contactId - Contact ID to process
     * @param {number} index - Current index
     * @param {number} total - Total contacts
     */
    async processContact(contactId, index, total) {
        try {
            console.log(`📄 Processing contact ${index}/${total}: ${contactId}`);

            // Get detailed contact information
            const encompassContact = await this.borrowerService.getBorrowerContactDetails(contactId);
            console.log(`   ✅ Encompass: ${encompassContact.firstName} ${encompassContact.lastName}`);
            console.log(`   📧 Email: ${encompassContact.personalEmail || encompassContact.businessEmail || 'N/A'}`);
            console.log(`   📞 Phone: ${encompassContact.homePhone || encompassContact.workPhone || encompassContact.mobilePhone || 'N/A'}`);

            // Transform to GoHighLevel format
            const ghlContactData = this.borrowerService.transformToGHLFormat(encompassContact);

            // Push to GoHighLevel
            const ghlResult = await this.ghlClient.createOrUpdateContact(ghlContactData);
            
            if (ghlResult.success) {
                console.log(`   ✅ GoHighLevel: Contact ${ghlResult.action} successfully`);
                console.log(`   🆔 GHL Contact ID: ${ghlResult.contactId || 'N/A'}`);
                
                // Create opportunity if configured
                let opportunityResult = null;
                if (this.ghlClient.pipelineId && this.ghlClient.pipelineStageId && ghlResult.contactId) {
                    console.log(`   🎯 Creating opportunity for contact...`);
                    opportunityResult = await this.ghlClient.createOpportunity(ghlResult.contactId, ghlContactData);
                    
                    if (opportunityResult && opportunityResult.success) {
                        console.log(`   ✅ Opportunity ${opportunityResult.isNew ? 'created' : 'found'}: ${opportunityResult.opportunityId}`);
                    } else {
                        console.log(`   ⚠️ Opportunity creation skipped or failed`);
                    }
                } else {
                    console.log(`   ℹ️ Opportunity creation skipped (missing pipeline configuration)`);
                }
                
                this.processedContacts.push({
                    encompassId: contactId,
                    ghlContactId: ghlResult.contactId,
                    ghlOpportunityId: opportunityResult?.opportunityId || null,
                    action: ghlResult.action,
                    opportunityAction: opportunityResult?.isNew ? 'created' : (opportunityResult?.success ? 'found' : 'failed'),
                    name: `${encompassContact.firstName || ''} ${encompassContact.lastName || ''}`.trim(),
                    email: encompassContact.personalEmail || encompassContact.businessEmail,
                    phone: encompassContact.homePhone || encompassContact.workPhone || encompassContact.mobilePhone,
                    processedAt: new Date().toISOString()
                });
            } else {
                console.log(`   ❌ Failed to create/update in GoHighLevel`);
            }

        } catch (error) {
            console.error(`   ❌ Error processing contact ${contactId}:`, error.message);
            this.errors.push({
                contactId,
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }

    /**
     * Generate integration summary
     */
    generateSummary() {
        const created = this.processedContacts.filter(c => c.action === 'created').length;
        const updated = this.processedContacts.filter(c => c.action === 'updated').length;
        const opportunitiesCreated = this.processedContacts.filter(c => c.opportunityAction === 'created').length;
        const opportunitiesFound = this.processedContacts.filter(c => c.opportunityAction === 'found').length;
        const opportunitiesFailed = this.processedContacts.filter(c => c.opportunityAction === 'failed').length;
        
        console.log('🎉 Integration completed!');
        console.log('='.repeat(60));
        console.log(`📥 Total contacts processed: ${this.processedContacts.length}`);
        console.log(`✨ New contacts created: ${created}`);
        console.log(`🔄 Existing contacts updated: ${updated}`);
        console.log(`🎯 New opportunities created: ${opportunitiesCreated}`);
        console.log(`🔍 Existing opportunities found: ${opportunitiesFound}`);
        console.log(`⚠️ Opportunity failures: ${opportunitiesFailed}`);
        console.log(`❌ Errors encountered: ${this.errors.length}`);
        console.log(`🌐 Total API calls made: ${this.ghlClient.apiCallCount}`);
        console.log(`⏱️ Total time: ${((Date.now() - this.startTime) / 1000).toFixed(2)} seconds`);
        console.log('='.repeat(60));

        if (this.errors.length > 0) {
            console.log('\n⚠️ Errors encountered:');
            this.errors.forEach((error, index) => {
                console.log(`   ${index + 1}. Contact ${error.contactId}: ${error.error}`);
            });
        }
    }

    /**
     * Save integration results to file
     */
    async saveResults() {
        const created = this.processedContacts.filter(c => c.action === 'created').length;
        const updated = this.processedContacts.filter(c => c.action === 'updated').length;
        const opportunitiesCreated = this.processedContacts.filter(c => c.opportunityAction === 'created').length;
        const opportunitiesFound = this.processedContacts.filter(c => c.opportunityAction === 'found').length;
        const opportunitiesFailed = this.processedContacts.filter(c => c.opportunityAction === 'failed').length;

        const resultsFile = path.join(this.dataDir, `integration-results-${Date.now()}.json`);
        
        const results = {
            summary: {
                totalProcessed: this.processedContacts.length,
                contactsCreated: created,
                contactsUpdated: updated,
                opportunitiesCreated,
                opportunitiesFound,
                opportunitiesFailed,
                errors: this.errors.length,
                apiCalls: this.ghlClient.apiCallCount,
                elapsedTime: (Date.now() - this.startTime) / 1000,
                completedAt: new Date().toISOString()
            },
            processedContacts: this.processedContacts,
            errors: this.errors
        };

        fs.writeFileSync(resultsFile, JSON.stringify(results, null, 2));
        console.log(`💾 Results saved to: ${resultsFile}`);
    }
}

// CLI execution
async function main() {
    const contactLimit = parseInt(process.argv[2]) || 10;
    
    const integration = new IntegrationService();
    
    try {
        await integration.runIntegration(contactLimit);
        console.log('\n✅ Integration completed successfully!');
    } catch (error) {
        console.error('❌ Integration failed:', error.message);
        process.exit(1);
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = IntegrationService;
