const axios = require('axios');
const fs = require('fs');
require('dotenv').config();

// Configuration
const baseUrl = 'https://api.elliemae.com';
const username = process.env.ENCOMPASS_USERNAME;
const password = process.env.ENCOMPASS_PASSWORD;
const clientId = process.env.ENCOMPASS_CLIENT_ID;
const clientSecret = process.env.ENCOMPASS_CLIENT_SECRET;
const instanceId = process.env.ENCOMPASS_INSTANCE_ID;

console.log('🔍 Fetching ALL Borrower Contacts with Pagination Testing');
console.log('='.repeat(60));

async function getAccessToken() {
  try {
    console.log('🔐 Getting access token...');
    
    const tokenResponse = await axios.post(`${baseUrl}/oauth2/v1/token`, 
      `grant_type=password&username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`,
      {
        headers: {
          'Authorization': `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`,
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );
    
    console.log('✅ Access token obtained successfully');
    return tokenResponse.data.access_token;
  } catch (error) {
    console.error('❌ Error getting access token:', error.response?.data || error.message);
    throw error;
  }
}

async function fetchAllBorrowerContacts(accessToken) {
  const headers = {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  };

  console.log('\n📋 Fetching All Borrower Contacts with Pagination');
  console.log('-'.repeat(50));

  const results = {
    timestamp: new Date().toISOString(),
    total_contacts: 0,
    all_contacts: [],
    pagination_tests: [],
    api_behavior: {},
    errors: []
  };

  // Test different pagination strategies
  const paginationTests = [
    { name: 'Small Pages (10)', limit: 10 },
    { name: 'Medium Pages (50)', limit: 50 },
    { name: 'Large Pages (100)', limit: 100 },
    { name: 'Very Large Pages (500)', limit: 500 }
  ];

  for (const test of paginationTests) {
    console.log(`\n🧪 Testing ${test.name}`);
    
    try {
      let allContacts = [];
      let currentStart = 0;
      let hasMore = true;
      let pageCount = 0;
      let maxPages = 20; // Limit to prevent infinite loops
      
      while (hasMore && pageCount < maxPages) {
        console.log(`📄 Fetching page ${pageCount + 1} (start: ${currentStart}, limit: ${test.limit})`);
        
        const payload = {
          start: currentStart,
          limit: test.limit
        };
        
        const response = await axios.post(
          `${baseUrl}/encompass/v1/borrowerContactSelector/`,
          payload,
          { headers }
        );
        
        if (response.data && Array.isArray(response.data)) {
          const contactsInPage = response.data.length;
          console.log(`   📈 Received ${contactsInPage} contacts`);
          
          allContacts.push(...response.data);
          pageCount++;
          
          // Check if we should continue
          if (contactsInPage < test.limit) {
            console.log(`   🏁 Last page reached (received ${contactsInPage} < ${test.limit})`);
            hasMore = false;
          } else {
            currentStart += test.limit;
          }
          
          // Add delay to avoid rate limiting
          await new Promise(resolve => setTimeout(resolve, 500));
          
        } else {
          console.log(`   ❌ Unexpected response format`);
          hasMore = false;
        }
      }
      
      const testResult = {
        test_name: test.name,
        limit: test.limit,
        pages_fetched: pageCount,
        total_contacts: allContacts.length,
        pagination_working: pageCount > 1,
        last_page_size: allContacts.length % test.limit || test.limit
      };
      
      results.pagination_tests.push(testResult);
      
      console.log(`✅ ${test.name} Complete:`);
      console.log(`   📊 Total contacts: ${allContacts.length}`);
      console.log(`   📄 Pages fetched: ${pageCount}`);
      console.log(`   🔄 Pagination working: ${pageCount > 1 ? 'YES' : 'NO'}`);
      
      // Store the largest dataset
      if (allContacts.length > results.total_contacts) {
        results.total_contacts = allContacts.length;
        results.all_contacts = allContacts;
      }
      
    } catch (error) {
      console.error(`❌ Error in ${test.name}:`, error.response?.status, error.response?.data || error.message);
      results.errors.push({
        test: test.name,
        error: error.response?.data || error.message,
        status: error.response?.status
      });
    }
  }

  // Test specific contact details
  if (results.all_contacts.length > 0) {
    console.log('\n🔍 Testing Individual Contact Details');
    
    try {
      const sampleContact = results.all_contacts[0];
      console.log(`📋 Testing contact ID: ${sampleContact.id}`);
      
      const contactResponse = await axios.get(
        `${baseUrl}/encompass/v1/borrowerContacts/${sampleContact.id}`,
        { headers }
      );
      
      console.log(`✅ Individual contact fetch successful`);
      console.log(`📊 Contact fields:`, Object.keys(contactResponse.data));
      
      results.api_behavior.individual_contact_fields = Object.keys(contactResponse.data);
      results.api_behavior.sample_contact = contactResponse.data;
      
    } catch (error) {
      console.error('❌ Error fetching individual contact:', error.response?.data || error.message);
      results.errors.push({
        test: 'Individual Contact Fetch',
        error: error.response?.data || error.message,
        status: error.response?.status
      });
    }
  }

  return results;
}

async function main() {
  try {
    const accessToken = await getAccessToken();
    const results = await fetchAllBorrowerContacts(accessToken);
    
    // Save comprehensive results
    const timestamp = Date.now();
    const summaryFilename = `data/borrower-contacts-complete-${timestamp}.json`;
    const contactsFilename = `data/borrower-contacts-all-data-${timestamp}.json`;
    
    // Save summary (without all contact data to keep file manageable)
    const summary = {
      ...results,
      all_contacts: `Saved separately to ${contactsFilename} (${results.all_contacts.length} contacts)`
    };
    fs.writeFileSync(summaryFilename, JSON.stringify(summary, null, 2));
    
    // Save all contacts data
    const contactsData = {
      timestamp: results.timestamp,
      total_contacts: results.total_contacts,
      contacts: results.all_contacts
    };
    fs.writeFileSync(contactsFilename, JSON.stringify(contactsData, null, 2));
    
    console.log('\n📊 FINAL SUMMARY');
    console.log('='.repeat(50));
    console.log(`✅ Total borrower contacts found: ${results.total_contacts}`);
    console.log(`🧪 Pagination tests completed: ${results.pagination_tests.length}`);
    console.log(`❌ Errors encountered: ${results.errors.length}`);
    console.log(`💾 Summary saved to: ${summaryFilename}`);
    console.log(`💾 All contacts saved to: ${contactsFilename}`);
    
    console.log('\n🔄 PAGINATION TEST RESULTS:');
    results.pagination_tests.forEach(test => {
      console.log(`   ${test.test_name}:`);
      console.log(`     📊 Contacts: ${test.total_contacts}`);
      console.log(`     📄 Pages: ${test.pages_fetched}`);
      console.log(`     🔄 Pagination: ${test.pagination_working ? '✅ YES' : '❌ NO'}`);
    });
    
    if (results.errors.length > 0) {
      console.log('\n❌ ERRORS:');
      results.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error.test}: ${error.status} - ${JSON.stringify(error.error)}`);
      });
    }
    
  } catch (error) {
    console.error('💥 Fatal error:', error.message);
    process.exit(1);
  }
}

// Run the comprehensive test
main();
