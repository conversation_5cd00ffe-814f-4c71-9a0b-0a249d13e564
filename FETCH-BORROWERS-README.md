# Fetch Borrowers Script

This script fetches all borrower contacts from the Encompass API and saves them to a JSON file for analysis and backup purposes.

## Features

- ✅ **Automatic Authentication** - Uses the same EncompassAuth utility as the main service
- ✅ **Pagination Support** - Automatically handles large datasets with pagination
- ✅ **Rate Limiting** - Built-in delays to respect API limits
- ✅ **Progress Tracking** - Shows real-time progress during fetching
- ✅ **JSON Export** - Saves data in structured JSON format with metadata
- ✅ **Error Handling** - Comprehensive error handling and logging
- ✅ **Data Directory** - Automatically creates `data/` directory for output files

## Usage

### Quick Start
```bash
npm run fetch-borrowers
```

### Direct Execution
```bash
node fetch-borrowers.js
```

## Configuration

The script uses the same environment variables as the main integration:

```env
ENCOMPASS_API_URL=https://api.elliemae.com
ENCOMPASS_CLIENT_ID=your_client_id
ENCOMPASS_CLIENT_SECRET=your_client_secret
ENCOMPASS_USERNAME=your_username@encompass:instance_id
ENCOMPASS_PASSWORD=your_password
```

## Output

### File Location
Files are saved to the `data/` directory with timestamp-based names:
```
data/borrower-contacts-2025-07-16T20-21-35-734Z.json
```

### JSON Structure
```json
{
  "contacts": [
    {
      "Contact.FirstName": "John",
      "Contact.LastName": "Doe",
      "Contact.PersonalEmail": "<EMAIL>",
      "Contact.HomePhone": "555-1234",
      "Contact.MobilePhone": "555-5678",
      "Contact.WorkPhone": "555-9012"
    }
  ],
  "totalRecords": 1,
  "fetchedAt": "2025-07-16T20:21:35.733Z",
  "summary": {
    "totalFetched": 1,
    "totalAvailable": 1,
    "completionPercentage": "100.00"
  }
}
```

### Fields Retrieved
- `Contact.FirstName` - Borrower's first name
- `Contact.LastName` - Borrower's last name
- `Contact.PersonalEmail` - Personal email address
- `Contact.HomePhone` - Home phone number
- `Contact.MobilePhone` - Mobile phone number
- `Contact.WorkPhone` - Work phone number

## Sample Output

```
[2025-07-16T20:20:51.280Z] 🎯 Encompass Borrower Fetcher Started
[2025-07-16T20:20:51.284Z] 🔗 API URL: https://api.elliemae.com
[2025-07-16T20:20:51.285Z] 👤 Username: contractoradmin@encompass:BE11140034
[2025-07-16T20:20:51.285Z] 🚀 Starting to fetch all borrower contacts...
[2025-07-16T20:20:51.286Z] Getting Encompass access token...
[2025-07-16T20:20:54.672Z] ✅ Encompass access token obtained successfully
[2025-07-16T20:20:54.672Z] 🌐 Getting 1000 borrower contacts starting from 1
[2025-07-16T20:21:35.731Z] ✅ Completed fetching all contacts: 0/0
[2025-07-16T20:21:35.736Z] 💾 Saved 0 contacts to: data/borrower-contacts-2025-07-16T20-21-35-734Z.json
[2025-07-16T20:21:35.737Z] 📁 File size: 0.18 KB

📋 FETCH SUMMARY:
   Total Contacts Fetched: 0
   Total Available: 0
   Completion: 0%
   File: data/borrower-contacts-2025-07-16T20-21-35-734Z.json
   Timestamp: 2025-07-16T20:21:35.733Z
[2025-07-16T20:21:35.742Z] 🎉 Script completed successfully!
```

## Technical Details

### Pagination
- Fetches up to 1000 contacts per API call (maximum allowed)
- Automatically handles pagination for large datasets
- Includes 1-second delay between requests for rate limiting

### Authentication
- Uses the same `EncompassAuth` utility as the main service
- Automatic token refresh when needed
- Secure credential handling via environment variables

### Error Handling
- Comprehensive error logging with timestamps
- Graceful failure with detailed error messages
- Automatic retry logic built into the auth utility

## Integration with Main Service

This script uses the same authentication and API patterns as the main Encompass-GoHighLevel integration service, ensuring consistency and reliability.

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Verify environment variables are set correctly
   - Check username format: `username@encompass:instance_id`
   - Ensure credentials have proper API access

2. **No Data Returned**
   - This is normal for test environments
   - Production environments will return actual borrower data
   - Check API permissions for borrower contact access

3. **Rate Limiting**
   - Script includes built-in rate limiting
   - If you encounter 429 errors, the delays may need to be increased

### Support

For issues related to this script, check:
1. Environment variable configuration
2. Encompass API credentials and permissions
3. Network connectivity to Encompass API
4. Log output for specific error messages
