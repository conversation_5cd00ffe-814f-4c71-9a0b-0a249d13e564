const cron = require('node-cron');
const express = require('express');
const { validateConfig } = require('./config');
const SyncService = require('./syncService');
const leadDatabase = require('./leadDatabase');
const logger = require('./logger');

class IntegrationApp {
  constructor() {
    this.syncService = new SyncService();
    this.isRunning = false;
    this.syncInProgress = false;
    this.lastSyncTime = null;
    this.lastSyncResults = null;
    this.app = express();
    this.setupExpressApp();
  }

  setupExpressApp() {
    this.app.use(express.json());
    this.app.use(express.urlencoded({ extended: true }));

    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.status(200).json({
        status: 'healthy',
        service: 'Encompass to GoHighLevel Integration',
        version: '1.0.0'
      });
    });

    // Status endpoint
    this.app.get('/status', (req, res) => {
      const db = leadDatabase.load();
      const stats = leadDatabase.getStats(db);
      const recentActivity = leadDatabase.getRecentActivity(db, 5);

      res.json({
        status: this.syncInProgress ? 'in_progress' : 'idle',
        isRunning: this.isRunning,
        lastSync: this.lastSyncTime,
        lastResults: this.lastSyncResults,
        database: {
          lastSync: db.lastSync,
          totalProcessedLeads: leadDatabase.getProcessedLeadsCount(db),
          stats: stats,
          recentActivity: recentActivity
        }
      });
    });

    // Trigger sync endpoint
    this.app.post('/api/sync', async (req, res) => {
      if (this.syncInProgress) {
        return res.status(409).json({
          error: 'Sync already in progress',
          status: 'in_progress'
        });
      }

      try {
        res.json({
          status: 'started',
          message: 'Sync started successfully',
          startedAt: new Date().toISOString()
        });

        // Run sync in background
        this.runSyncProcess();
      } catch (error) {
        logger.error('Failed to start sync', error);
        res.status(500).json({
          error: 'Failed to start sync',
          message: error.message
        });
      }
    });

    // Database stats endpoint
    this.app.get('/api/stats', (req, res) => {
      try {
        const db = leadDatabase.load();
        const stats = leadDatabase.getStats(db);
        const recentActivity = leadDatabase.getRecentActivity(db, 20);

        res.json({
          database: {
            lastSync: db.lastSync,
            totalProcessedLeads: leadDatabase.getProcessedLeadsCount(db),
            stats: stats
          },
          recentActivity: recentActivity
        });
      } catch (error) {
        logger.error('Failed to get database stats', error);
        res.status(500).json({
          error: 'Failed to get database stats',
          message: error.message
        });
      }
    });

    // Root endpoint
    this.app.get('/', (req, res) => {
      const db = leadDatabase.load();
      const stats = leadDatabase.getStats(db);

      res.json({
        service: 'Encompass to GoHighLevel Integration',
        version: '1.0.0',
        status: this.syncInProgress ? 'sync_in_progress' : 'ready',
        totalProcessedLeads: leadDatabase.getProcessedLeadsCount(db),
        stats: stats,
        endpoints: {
          health: '/health',
          status: '/status',
          stats: '/api/stats',
          triggerSync: 'POST /api/sync'
        }
      });
    });
  }

  async runSyncProcess() {
    if (this.syncInProgress) {
      logger.warn('Sync already in progress, skipping');
      return;
    }

    this.syncInProgress = true;
    try {
      logger.info('Starting sync process...');
      const results = await this.syncService.syncLeads();
      this.lastSyncResults = results;
      this.lastSyncTime = new Date().toISOString();
      logger.info('Sync process completed successfully', results);
    } catch (error) {
      logger.error('Sync process failed', error);
      this.lastSyncResults = { error: error.message };
      this.lastSyncTime = new Date().toISOString();
    } finally {
      this.syncInProgress = false;
    }
  }

  async start() {
    try {
      logger.info('Starting Encompass to GoHighLevel Integration...');

      // Validate configuration
      validateConfig();
      logger.info('✓ Configuration validated');

      // Test API connections
      const connectionTest = await this.syncService.testConnection();
      if (!connectionTest) {
        throw new Error('API connection test failed');
      }

      // Start Express server
      const PORT = process.env.PORT || 3000;
      this.app.listen(PORT, () => {
        logger.info(`Web interface started on port ${PORT}`);
        logger.info(`Health check: http://localhost:${PORT}/health`);
        logger.info(`Status: http://localhost:${PORT}/status`);
      });

      // Perform initial sync
      logger.info('Performing initial synchronization...');
      await this.runSyncProcess();

      // Schedule recurring sync
      this.scheduleSync();

      this.isRunning = true;
      logger.info('Integration started successfully');

      // Keep the process running
      this.setupGracefulShutdown();

    } catch (error) {
      logger.error('Failed to start integration', error);
      process.exit(1);
    }
  }

  scheduleSync() {
    const { config } = require('./config');
    const intervalMinutes = config.sync.intervalMinutes;
    
    // Create cron expression for the specified interval
    const cronExpression = `*/${intervalMinutes} * * * *`;
    
    logger.info(`Scheduling sync to run every ${intervalMinutes} minutes`);
    
    cron.schedule(cronExpression, async () => {
      if (this.isRunning && !this.syncInProgress) {
        try {
          logger.info('Running scheduled sync...');
          await this.runSyncProcess();
        } catch (error) {
          logger.error('Scheduled sync failed', error);
        }
      } else if (this.syncInProgress) {
        logger.info('Skipping scheduled sync - sync already in progress');
      }
    });
  }

  setupGracefulShutdown() {
    const shutdown = (signal) => {
      logger.info(`Received ${signal}. Shutting down gracefully...`);
      this.isRunning = false;
      process.exit(0);
    };

    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGTERM', () => shutdown('SIGTERM'));
  }

  async runOnce() {
    try {
      logger.info('Running one-time synchronization...');
      
      // Validate configuration
      validateConfig();
      
      // Test connections
      await this.syncService.testConnection();
      
      // Run sync
      const results = await this.syncService.syncLeads();
      
      logger.info('One-time sync completed', results);
      return results;
    } catch (error) {
      logger.error('One-time sync failed', error);
      throw error;
    }
  }
}

// Check if this file is being run directly
if (require.main === module) {
  const app = new IntegrationApp();
  
  // Check for command line arguments
  const args = process.argv.slice(2);
  
  if (args.includes('--once')) {
    // Run once and exit
    app.runOnce()
      .then(() => process.exit(0))
      .catch(() => process.exit(1));
  } else {
    // Start the continuous integration
    app.start();
  }
}

module.exports = IntegrationApp;
