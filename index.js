const cron = require('node-cron');
const { validateConfig } = require('./config');
const SyncService = require('./syncService');
const logger = require('./logger');

class IntegrationApp {
  constructor() {
    this.syncService = new SyncService();
    this.isRunning = false;
  }

  async start() {
    try {
      logger.info('Starting Encompass to GoHighLevel Integration...');
      
      // Validate configuration
      validateConfig();
      logger.info('✓ Configuration validated');
      
      // Test API connections
      const connectionTest = await this.syncService.testConnection();
      if (!connectionTest) {
        throw new Error('API connection test failed');
      }
      
      // Perform initial sync
      logger.info('Performing initial synchronization...');
      await this.syncService.syncLeads();
      
      // Schedule recurring sync
      this.scheduleSync();
      
      this.isRunning = true;
      logger.info('Integration started successfully');
      
      // Keep the process running
      this.setupGracefulShutdown();
      
    } catch (error) {
      logger.error('Failed to start integration', error);
      process.exit(1);
    }
  }

  scheduleSync() {
    const { config } = require('./config');
    const intervalMinutes = config.sync.intervalMinutes;
    
    // Create cron expression for the specified interval
    const cronExpression = `*/${intervalMinutes} * * * *`;
    
    logger.info(`Scheduling sync to run every ${intervalMinutes} minutes`);
    
    cron.schedule(cronExpression, async () => {
      if (this.isRunning) {
        try {
          logger.info('Running scheduled sync...');
          await this.syncService.syncLeads();
        } catch (error) {
          logger.error('Scheduled sync failed', error);
        }
      }
    });
  }

  setupGracefulShutdown() {
    const shutdown = (signal) => {
      logger.info(`Received ${signal}. Shutting down gracefully...`);
      this.isRunning = false;
      process.exit(0);
    };

    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGTERM', () => shutdown('SIGTERM'));
  }

  async runOnce() {
    try {
      logger.info('Running one-time synchronization...');
      
      // Validate configuration
      validateConfig();
      
      // Test connections
      await this.syncService.testConnection();
      
      // Run sync
      const results = await this.syncService.syncLeads();
      
      logger.info('One-time sync completed', results);
      return results;
    } catch (error) {
      logger.error('One-time sync failed', error);
      throw error;
    }
  }
}

// Check if this file is being run directly
if (require.main === module) {
  const app = new IntegrationApp();
  
  // Check for command line arguments
  const args = process.argv.slice(2);
  
  if (args.includes('--once')) {
    // Run once and exit
    app.runOnce()
      .then(() => process.exit(0))
      .catch(() => process.exit(1));
  } else {
    // Start the continuous integration
    app.start();
  }
}

module.exports = IntegrationApp;
