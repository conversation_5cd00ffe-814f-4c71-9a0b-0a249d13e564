{"timestamp": "2025-07-04T16:24:00.364Z", "totalFolders": 16, "folders": [{"name": "(Archive)", "activityRules": [{"action": "Originate", "ruleValue": "<PERSON><PERSON>"}, {"action": "DuplicateFrom", "ruleValue": "Permit"}, {"action": "DuplicateInto", "ruleValue": "<PERSON><PERSON>"}, {"action": "Import", "ruleValue": "<PERSON><PERSON>"}], "folderType": "Archive", "isExternalOrganization": false, "loanGuid": null}, {"name": "Active Pipeline", "activityRules": [{"action": "Originate", "ruleValue": "<PERSON><PERSON>"}, {"action": "DuplicateFrom", "ruleValue": "Permit"}, {"action": "DuplicateInto", "ruleValue": "<PERSON><PERSON>"}, {"action": "Import", "ruleValue": "<PERSON><PERSON>"}], "folderType": "Regular", "isExternalOrganization": false, "loanGuid": null}, {"name": "Adverse Loans", "activityRules": [{"action": "Originate", "ruleValue": "<PERSON><PERSON>"}, {"action": "DuplicateFrom", "ruleValue": "Permit"}, {"action": "DuplicateInto", "ruleValue": "<PERSON><PERSON>"}, {"action": "Import", "ruleValue": "<PERSON><PERSON>"}], "folderType": "Archive", "isExternalOrganization": false, "loanGuid": null}, {"name": "Closing", "activityRules": [{"action": "Originate", "ruleValue": "<PERSON><PERSON>"}, {"action": "DuplicateFrom", "ruleValue": "<PERSON><PERSON>"}, {"action": "DuplicateInto", "ruleValue": "<PERSON><PERSON>"}, {"action": "Import", "ruleValue": "<PERSON><PERSON>"}], "folderType": "Regular", "isExternalOrganization": false, "loanGuid": null}, {"name": "Completed Loans", "activityRules": [{"action": "Originate", "ruleValue": "<PERSON><PERSON>"}, {"action": "DuplicateFrom", "ruleValue": "Permit"}, {"action": "DuplicateInto", "ruleValue": "<PERSON><PERSON>"}, {"action": "Import", "ruleValue": "<PERSON><PERSON>"}], "folderType": "Archive", "isExternalOrganization": false, "loanGuid": null}, {"name": "Dead Loans", "activityRules": [{"action": "Originate", "ruleValue": "<PERSON><PERSON>"}, {"action": "DuplicateFrom", "ruleValue": "Permit"}, {"action": "DuplicateInto", "ruleValue": "<PERSON><PERSON>"}, {"action": "Import", "ruleValue": "Permit"}], "folderType": "Archive", "isExternalOrganization": false, "loanGuid": null}, {"name": "DO NOT USE - INTERNAL", "activityRules": [{"action": "Originate", "ruleValue": "Permit"}, {"action": "DuplicateFrom", "ruleValue": "Permit"}, {"action": "DuplicateInto", "ruleValue": "<PERSON><PERSON>"}, {"action": "Import", "ruleValue": "Permit"}], "folderType": "Regular", "isExternalOrganization": false, "loanGuid": null}, {"name": "Internal - Active Loan", "activityRules": [{"action": "Originate", "ruleValue": "Permit"}, {"action": "DuplicateFrom", "ruleValue": "Permit"}, {"action": "DuplicateInto", "ruleValue": "Permit"}, {"action": "Import", "ruleValue": "Permit"}], "folderType": "Regular", "isExternalOrganization": false, "loanGuid": null}, {"name": "Leads", "activityRules": [{"action": "Originate", "ruleValue": "<PERSON><PERSON>"}, {"action": "DuplicateFrom", "ruleValue": "<PERSON><PERSON>"}, {"action": "DuplicateInto", "ruleValue": "<PERSON><PERSON>"}, {"action": "Import", "ruleValue": "<PERSON><PERSON>"}], "folderType": "Regular", "isExternalOrganization": false, "loanGuid": null}, {"name": "My Pipeline", "activityRules": [{"action": "Originate", "ruleValue": "Permit"}, {"action": "DuplicateFrom", "ruleValue": "Permit"}, {"action": "DuplicateInto", "ruleValue": "Permit"}, {"action": "Import", "ruleValue": "Permit"}], "folderType": "Regular", "isExternalOrganization": false, "loanGuid": null}, {"name": "Online Applications", "activityRules": [{"action": "Originate", "ruleValue": "Permit"}, {"action": "DuplicateFrom", "ruleValue": "<PERSON><PERSON>"}, {"action": "DuplicateInto", "ruleValue": "<PERSON><PERSON>"}, {"action": "Import", "ruleValue": "Permit"}], "folderType": "Archive", "isExternalOrganization": false, "loanGuid": null}, {"name": "Post-Closing", "activityRules": [{"action": "Originate", "ruleValue": "<PERSON><PERSON>"}, {"action": "DuplicateFrom", "ruleValue": "<PERSON><PERSON>"}, {"action": "DuplicateInto", "ruleValue": "<PERSON><PERSON>"}, {"action": "Import", "ruleValue": "<PERSON><PERSON>"}], "folderType": "Regular", "isExternalOrganization": false, "loanGuid": null}, {"name": "Test Folder", "activityRules": [{"action": "Originate", "ruleValue": "Permit"}, {"action": "DuplicateFrom", "ruleValue": "Permit"}, {"action": "DuplicateInto", "ruleValue": "Permit"}, {"action": "Import", "ruleValue": "Permit"}], "folderType": "Regular", "isExternalOrganization": false, "loanGuid": null}, {"name": "Testing_Training", "activityRules": [{"action": "Originate", "ruleValue": "Permit"}, {"action": "DuplicateFrom", "ruleValue": "Permit"}, {"action": "DuplicateInto", "ruleValue": "Permit"}, {"action": "Import", "ruleValue": "<PERSON><PERSON>"}], "folderType": "Archive", "isExternalOrganization": false, "loanGuid": null}, {"name": "TPO Active", "activityRules": [{"action": "Originate", "ruleValue": "Permit"}, {"action": "DuplicateFrom", "ruleValue": "Permit"}, {"action": "DuplicateInto", "ruleValue": "Permit"}, {"action": "Import", "ruleValue": "Permit"}], "folderType": "Regular", "isExternalOrganization": false, "loanGuid": null}, {"name": "TPO Pending", "activityRules": [{"action": "Originate", "ruleValue": "Permit"}, {"action": "DuplicateFrom", "ruleValue": "Permit"}, {"action": "DuplicateInto", "ruleValue": "Permit"}, {"action": "Import", "ruleValue": "Permit"}], "folderType": "Regular", "isExternalOrganization": false, "loanGuid": null}], "sampleLeads": []}