# Encompass to GoHighLevel Integration

This Node.js application automatically synchronizes leads from Encompass mortgage software to GoHighLevel CRM using their respective APIs.

## Features

- **Automated Sync**: Runs continuously with configurable intervals (default: every 5 minutes)
- **Duplicate Prevention**: Checks for existing contacts before creating new ones
- **Error Handling**: Comprehensive logging and error recovery
- **Configuration**: Environment-based configuration for easy deployment
- **Testing**: Built-in connection testing for both APIs

## Prerequisites

1. **Encompass API Access**:
   - Client ID and Client Secret
   - Username and Password with API access
   - Instance ID

2. **GoHighLevel API Access**:
   - API Key with contact management permissions
   - Location ID

## Installation

1. Clone or download this project
2. Install dependencies:
   ```bash
   npm install
   ```

3. Copy the environment template:
   ```bash
   cp .env.example .env
   ```

4. Edit `.env` file with your API credentials:
   ```env
   # Encompass API Configuration
   ENCOMPASS_API_URL=https://api.elliemae.com
   ENCOMPASS_CLIENT_ID=your_encompass_client_id
   ENCOMPASS_CLIENT_SECRET=your_encompass_client_secret
   ENCOMPASS_USERNAME=your_encompass_username
   ENCOMPASS_PASSWORD=your_encompass_password
   ENCOMPASS_INSTANCE_ID=your_encompass_instance_id

   # GoHighLevel API Configuration
   GOHIGHLEVEL_API_URL=https://rest.gohighlevel.com/v1
   GOHIGHLEVEL_API_KEY=your_gohighlevel_api_key
   GOHIGHLEVEL_LOCATION_ID=your_gohighlevel_location_id

   # Integration Settings
   SYNC_INTERVAL_MINUTES=5
   LOG_LEVEL=info
   ```

## Usage

### Run Continuous Sync
```bash
npm start
```
This will start the integration and run continuously, syncing leads every 5 minutes (or your configured interval).

### Run One-Time Sync
```bash
node index.js --once
```
This will run a single synchronization and exit.

### Development Mode
```bash
npm run dev
```
Uses nodemon for automatic restarts during development.

## How It Works

1. **Authentication**: Connects to both Encompass and GoHighLevel APIs
2. **Data Retrieval**: Fetches recent leads from Encompass
3. **Data Transformation**: Converts Encompass loan data to GoHighLevel contact format
4. **Duplicate Check**: Searches for existing contacts by Encompass ID or email
5. **Sync**: Creates new contacts or updates existing ones in GoHighLevel
6. **Scheduling**: Repeats the process at configured intervals

## Data Mapping

The integration maps the following fields from Encompass to GoHighLevel:

| Encompass Field | GoHighLevel Field | Description |
|----------------|-------------------|-------------|
| Field 4002 | firstName | Borrower First Name |
| Field 4000 | lastName | Borrower Last Name |
| Field 4008 | email | Borrower Email |
| Field 4009 | phone | Borrower Phone |
| Field 4010 | address1 | Property Address |
| Field 4011 | city | Property City |
| Field 4012 | state | Property State |
| Field 4013 | postalCode | Property Zip Code |
| Field 1109 | loan_amount (custom) | Loan Amount |
| Field 4003 | loan_purpose (custom) | Loan Purpose |
| Loan Number/GUID | encompass_id (custom) | Encompass Identifier |

## Logging

The application provides detailed logging including:
- Sync start/completion times
- Number of leads processed, created, and updated
- Error details for troubleshooting
- API connection status

## Error Handling

- **API Failures**: Retries authentication and logs detailed error information
- **Data Validation**: Skips leads without essential information (email or phone)
- **Graceful Shutdown**: Handles SIGINT and SIGTERM signals properly

## Troubleshooting

### Common Issues

1. **Authentication Errors**:
   - Verify your API credentials in the `.env` file
   - Check that your Encompass user has API access permissions
   - Ensure your GoHighLevel API key has contact management permissions

2. **No Leads Found**:
   - Check the date range (default is last 24 hours)
   - Verify that leads exist in Encompass with the required fields
   - Check Encompass API permissions for loan data access

3. **Contact Creation Failures**:
   - Verify GoHighLevel location ID is correct
   - Check that required fields are being mapped correctly
   - Review GoHighLevel API rate limits

### Getting API Credentials

**Encompass**:
1. Contact your Encompass administrator
2. Request API access and credentials
3. Obtain Client ID, Client Secret, and Instance ID from Encompass Developer Connect

**GoHighLevel**:
1. Log into your GoHighLevel account
2. Go to Settings > Integrations > API
3. Generate an API key with contact permissions
4. Find your Location ID in the account settings

## Support

For issues related to:
- **Encompass API**: Contact ICE Mortgage Technology support
- **GoHighLevel API**: Contact GoHighLevel support
- **This Integration**: Check the logs for detailed error information

## License

MIT License - feel free to modify and distribute as needed.
