{"timestamp": "2025-07-04T17:50:04.656Z", "endpoints_tested": [{"endpoint": "V1 Get Borrower Contact List (POST)", "url": "/encompass/v1/borrowerContactSelector/", "status": "success", "response_size": 1000}, {"endpoint": "V1 Get a Borrower Contact (GET)", "url": "/encompass/v1/borrowerContacts/a26af9f3-2d41-4ae1-9a41-366437ff8aaa", "status": "success", "contact_id": "a26af9f3-2d41-4ae1-9a41-366437ff8aaa"}], "total_contacts_found": 1000, "pagination_details": {"borrower_contact_list": {"pages_tested": 1, "total_contacts": 1000, "pagination_working": false}}, "errors": [{"endpoint": "V1 View Borrower Contacts (with Pa<PERSON><PERSON>)", "error": {"summary": "Conflict", "details": "Invalid canonical name/s for fields list - id, firstName, lastName, email, phone, employerName, birthdate", "errorCode": "EBS-2508"}, "status": 409}]}