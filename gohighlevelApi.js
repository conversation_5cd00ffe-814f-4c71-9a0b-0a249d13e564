const axios = require('axios');
const { config } = require('./config');
const logger = require('./logger');

class GoHighLevelApi {
  constructor() {
    this.baseUrl = config.gohighlevel.apiUrl;
    this.apiKey = config.gohighlevel.apiKey;
    this.locationId = config.gohighlevel.locationId;
    this.pipelineId = config.gohighlevel.pipelineId;
    this.pipelineStageId = config.gohighlevel.pipelineStageId;
  }

  // Helper function to generate a placeholder email when lead email is missing or invalid
  generatePlaceholderEmail(lead) {
    const leadId = lead.id || Date.now();
    return `${leadId}@updateEmail.com`;
  }

  // Helper function to generate a placeholder phone number when lead phone is missing or invalid
  generatePlaceholderPhone(lead) {
    const leadId = lead.id || Math.floor(Math.random() * 10000000000);
    const phoneNumber = String(leadId).padStart(10, '0');
    return phoneNumber;
  }

  // Helper function to validate and clean email
  validateAndCleanEmail(email) {
    if (!email || typeof email !== 'string') return null;
    const cleanedEmail = email.trim();
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(cleanedEmail) ? cleanedEmail : null;
  }

  // Helper function to validate and clean phone number
  validateAndCleanPhone(phone) {
    if (!phone || typeof phone !== 'string') return null;
    const cleanedPhone = phone.replace(/\D/g, '');
    return cleanedPhone.length >= 10 ? cleanedPhone : null;
  }

  // Format lead data for GoHighLevel compatibility
  formatLeadForGoHighLevel(leadData) {
    // Validate and clean email, generate placeholder if needed
    let email = this.validateAndCleanEmail(leadData.email);
    let isEmailGenerated = false;
    if (!email) {
      email = this.generatePlaceholderEmail(leadData);
      isEmailGenerated = true;
    }

    // Validate and clean phone, generate placeholder if needed
    let phone = this.validateAndCleanPhone(leadData.phone);
    let isPhoneGenerated = false;
    if (!phone) {
      phone = this.generatePlaceholderPhone(leadData);
      isPhoneGenerated = true;
    }

    const contactData = {
      firstName: leadData.firstName || '',
      lastName: leadData.lastName || '',
      name: `${leadData.firstName || ''} ${leadData.lastName || ''}`.trim(),
      email: email,
      phone: phone,
      address1: leadData.address || '',
      city: leadData.city || '',
      state: leadData.state || '',
      postalCode: leadData.zipCode || '',
      source: 'Encompass Integration',
      tags: ['Encompass Lead', 'Mortgage Lead'],
      locationId: this.locationId,
      customFields: [
        {
          key: 'loan_amount',
          field_value: leadData.loanAmount?.toString() || '0'
        },
        {
          key: 'loan_purpose',
          field_value: leadData.loanPurpose || ''
        },
        {
          key: 'encompass_id',
          field_value: leadData.id || ''
        },
        {
          key: 'property_address',
          field_value: leadData.address || ''
        }
      ]
    };

    // Add flags to indicate generated fields
    if (isEmailGenerated) {
      contactData.tags.push('Generated Email');
      contactData.customFields.push({ key: 'email_generated', field_value: 'true' });
      contactData.customFields.push({ key: 'original_email', field_value: leadData.email || 'none' });
    }

    if (isPhoneGenerated) {
      contactData.tags.push('Generated Phone');
      contactData.customFields.push({ key: 'phone_generated', field_value: 'true' });
      contactData.customFields.push({ key: 'original_phone', field_value: leadData.phone || 'none' });
    }

    return contactData;
  }

  async createContact(leadData) {
    try {
      logger.info(`Creating contact in GoHighLevel for ${leadData.firstName} ${leadData.lastName}`);

      const contactData = this.formatLeadForGoHighLevel(leadData);

      const response = await axios.post(`${this.baseUrl}/contacts/`, contactData, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'Version': '2021-07-28'
        }
      });

      const contactId = response.data.contact?.id;
      logger.info(`Successfully created contact in GoHighLevel with ID: ${contactId}`);

      // Log detailed information
      logger.info(`Contact Details:`, {
        contactId: contactId,
        name: `${leadData.firstName} ${leadData.lastName}`,
        email: contactData.email,
        phone: contactData.phone,
        encompassId: leadData.id,
        generatedEmail: contactData.tags.includes('Generated Email'),
        generatedPhone: contactData.tags.includes('Generated Phone')
      });

      return { contact: response.data.contact, success: true };
    } catch (error) {
      logger.error('Failed to create contact in GoHighLevel', {
        error: error.response?.data || error.message,
        leadData: {
          id: leadData.id,
          name: `${leadData.firstName} ${leadData.lastName}`,
          email: leadData.email,
          phone: leadData.phone
        }
      });
      return { contact: null, success: false, error: error.message };
    }
  }

  async updateContact(contactId, leadData) {
    try {
      logger.info(`Updating contact ${contactId} in GoHighLevel`);

      const contactData = this.formatLeadForGoHighLevel(leadData);
      // Remove locationId for update requests
      delete contactData.locationId;

      const response = await axios.put(`${this.baseUrl}/contacts/${contactId}`, contactData, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'Version': '2021-07-28'
        }
      });

      logger.info(`Successfully updated contact ${contactId} in GoHighLevel`);
      return { contact: response.data.contact, success: true };
    } catch (error) {
      logger.error('Failed to update contact in GoHighLevel', {
        error: error.response?.data || error.message,
        contactId: contactId,
        leadData: {
          id: leadData.id,
          name: `${leadData.firstName} ${leadData.lastName}`,
          email: leadData.email,
          phone: leadData.phone
        }
      });
      return { contact: null, success: false, error: error.message };
    }
  }

  async findContactByEmail(email) {
    try {
      const response = await axios.get(`${this.baseUrl}/contacts/`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'Version': '2021-07-28'
        },
        params: {
          locationId: this.locationId,
          email: email
        }
      });

      return response.data.contacts && response.data.contacts.length > 0
        ? response.data.contacts[0]
        : null;
    } catch (error) {
      logger.error('Failed to search for contact in GoHighLevel', {
        error: error.response?.data || error.message,
        email: email
      });
      return null;
    }
  }

  async findContactByEncompassId(encompassId) {
    try {
      const response = await axios.get(`${this.baseUrl}/contacts/`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'Version': '2021-07-28'
        },
        params: {
          locationId: this.locationId,
          query: encompassId
        }
      });

      // Look for contact with matching Encompass ID in custom fields
      const contacts = response.data.contacts || [];
      return contacts.find(contact => {
        const encompassIdField = contact.customFields?.find(field =>
          field.key === 'encompass_id' && field.field_value === encompassId
        );
        return !!encompassIdField;
      }) || null;
    } catch (error) {
      logger.error('Failed to search for contact by Encompass ID', {
        error: error.response?.data || error.message,
        encompassId: encompassId
      });
      return null;
    }
  }

  // Create an opportunity in GoHighLevel for a contact
  async createOpportunity(contactId, leadData) {
    // Skip if pipeline configuration is missing
    if (!this.pipelineId || !this.pipelineStageId) {
      logger.info(`Skipping opportunity creation for lead ${leadData.id} - pipeline configuration missing`);
      return { opportunity: null, success: false, reason: 'missing_pipeline_config' };
    }

    try {
      logger.info(`Creating opportunity in GoHighLevel for contact ${contactId}`);

      const opportunityData = {
        pipelineId: this.pipelineId,
        pipelineStageId: this.pipelineStageId,
        locationId: this.locationId,
        name: `${leadData.firstName || ''} ${leadData.lastName || ''}`.trim() || 'Mortgage Lead',
        status: "open",
        contactId: contactId,
        monetaryValue: leadData.loanAmount || 0,
        source: "Encompass Integration"
      };

      const response = await axios.post(`${this.baseUrl}/opportunities/`, opportunityData, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'Version': '2021-07-28'
        }
      });

      const opportunityId = response.data.opportunity?.id || response.data.id;
      logger.info(`Successfully created opportunity in GoHighLevel with ID: ${opportunityId}`);

      return { opportunity: response.data.opportunity || response.data, success: true };
    } catch (error) {
      logger.error('Failed to create opportunity in GoHighLevel', {
        error: error.response?.data || error.message,
        contactId: contactId,
        leadData: {
          id: leadData.id,
          name: `${leadData.firstName} ${leadData.lastName}`,
          loanAmount: leadData.loanAmount
        }
      });
      return { opportunity: null, success: false, error: error.message };
    }
  }

  // Check if a contact already has an opportunity
  async hasExistingOpportunity(contactId) {
    try {
      const response = await axios.get(`${this.baseUrl}/opportunities/search`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'Version': '2021-07-28'
        },
        params: {
          locationId: this.locationId,
          contactId: contactId
        }
      });

      const opportunities = response.data.opportunities || [];
      return opportunities.length > 0;
    } catch (error) {
      logger.warn('Failed to check for existing opportunities', {
        error: error.response?.data || error.message,
        contactId: contactId
      });
      return false; // Assume no opportunities if we can't check
    }
  }
}

module.exports = GoHighLevelApi;
