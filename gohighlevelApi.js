const axios = require('axios');
const { config } = require('./config');
const logger = require('./logger');

class GoHighLevelApi {
  constructor() {
    this.baseUrl = config.gohighlevel.apiUrl;
    this.apiKey = config.gohighlevel.apiKey;
    this.locationId = config.gohighlevel.locationId;
  }

  async createContact(leadData) {
    try {
      logger.info(`Creating contact in GoHighLevel for ${leadData.firstName} ${leadData.lastName}`);

      const contactData = {
        firstName: leadData.firstName,
        lastName: leadData.lastName,
        email: leadData.email,
        phone: leadData.phone,
        address1: leadData.address,
        city: leadData.city,
        state: leadData.state,
        postalCode: leadData.zipCode,
        source: leadData.source,
        tags: ['Encompass Lead', 'Mortgage Lead'],
        customFields: [
          {
            key: 'loan_amount',
            field_value: leadData.loanAmount?.toString() || '0'
          },
          {
            key: 'loan_purpose',
            field_value: leadData.loanPurpose || ''
          },
          {
            key: 'encompass_id',
            field_value: leadData.id || ''
          }
        ]
      };

      const response = await axios.post(`${this.baseUrl}/contacts/`, contactData, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      logger.info(`Successfully created contact in GoHighLevel with ID: ${response.data.contact?.id}`);
      return response.data;
    } catch (error) {
      logger.error('Failed to create contact in GoHighLevel', {
        error: error.response?.data || error.message,
        leadData: leadData
      });
      throw error;
    }
  }

  async updateContact(contactId, leadData) {
    try {
      logger.info(`Updating contact ${contactId} in GoHighLevel`);

      const contactData = {
        firstName: leadData.firstName,
        lastName: leadData.lastName,
        email: leadData.email,
        phone: leadData.phone,
        address1: leadData.address,
        city: leadData.city,
        state: leadData.state,
        postalCode: leadData.zipCode,
        customFields: [
          {
            key: 'loan_amount',
            field_value: leadData.loanAmount?.toString() || '0'
          },
          {
            key: 'loan_purpose',
            field_value: leadData.loanPurpose || ''
          }
        ]
      };

      const response = await axios.put(`${this.baseUrl}/contacts/${contactId}`, contactData, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      logger.info(`Successfully updated contact ${contactId} in GoHighLevel`);
      return response.data;
    } catch (error) {
      logger.error('Failed to update contact in GoHighLevel', {
        error: error.response?.data || error.message,
        contactId: contactId,
        leadData: leadData
      });
      throw error;
    }
  }

  async findContactByEmail(email) {
    try {
      const response = await axios.get(`${this.baseUrl}/contacts/`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        params: {
          email: email
        }
      });

      return response.data.contacts && response.data.contacts.length > 0 
        ? response.data.contacts[0] 
        : null;
    } catch (error) {
      logger.error('Failed to search for contact in GoHighLevel', {
        error: error.response?.data || error.message,
        email: email
      });
      return null;
    }
  }

  async findContactByEncompassId(encompassId) {
    try {
      const response = await axios.get(`${this.baseUrl}/contacts/`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        params: {
          query: encompassId
        }
      });

      // Look for contact with matching Encompass ID in custom fields
      const contacts = response.data.contacts || [];
      return contacts.find(contact => {
        const encompassIdField = contact.customFields?.find(field => 
          field.key === 'encompass_id' && field.field_value === encompassId
        );
        return !!encompassIdField;
      }) || null;
    } catch (error) {
      logger.error('Failed to search for contact by Encompass ID', {
        error: error.response?.data || error.message,
        encompassId: encompassId
      });
      return null;
    }
  }
}

module.exports = GoHighLevelApi;
