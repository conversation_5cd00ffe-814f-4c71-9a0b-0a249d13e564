const axios = require('axios');
const fs = require('fs');
require('dotenv').config();

// Configuration
const baseUrl = 'https://api.elliemae.com';
const username = process.env.ENCOMPASS_USERNAME;
const password = process.env.ENCOMPASS_PASSWORD;
const clientId = process.env.ENCOMPASS_CLIENT_ID;
const clientSecret = process.env.ENCOMPASS_CLIENT_SECRET;
const instanceId = process.env.ENCOMPASS_INSTANCE_ID;

console.log('🔍 Testing V1 View Borrower Contacts with Proper Pagination');
console.log('='.repeat(60));

async function getAccessToken() {
  try {
    console.log('🔐 Getting access token...');
    
    const tokenResponse = await axios.post(`${baseUrl}/oauth2/v1/token`, 
      `grant_type=password&username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`,
      {
        headers: {
          'Authorization': `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`,
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );
    
    console.log('✅ Access token obtained successfully');
    return tokenResponse.data.access_token;
  } catch (error) {
    console.error('❌ Error getting access token:', error.response?.data || error.message);
    throw error;
  }
}

async function testBorrowerContactsPagination(accessToken) {
  const headers = {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  };

  console.log('\n📋 Testing V1 View Borrower Contacts with Pagination');
  console.log('-'.repeat(50));

  const results = {
    timestamp: new Date().toISOString(),
    total_contacts_found: 0,
    all_contacts: [],
    pagination_details: {},
    api_responses: [],
    errors: []
  };

  try {
    // Test 1: First page with 1000 contacts (default)
    console.log('\n1️⃣ Testing first page with default limit (1000)');

    const firstPageUrl = `${baseUrl}/encompass/v1/borrowerContactSelector?start=1&limit=1000&cursorType=randomAccess`;

    // Add a basic filter as required by the API when using cursor
    const requestBody = {
      filter: {
        // Empty filter to get all contacts
      }
    };

    const firstPageResponse = await axios.post(firstPageUrl, requestBody, { headers });
    
    console.log(`✅ First page - Status: ${firstPageResponse.status}`);
    console.log(`📊 Response structure:`, Object.keys(firstPageResponse.data || {}));
    
    let contacts = [];
    let cursor = null;
    let hasMore = false;
    
    if (Array.isArray(firstPageResponse.data)) {
      contacts = firstPageResponse.data;
      console.log(`📈 Found ${contacts.length} contacts (direct array)`);
    } else if (firstPageResponse.data && firstPageResponse.data.contacts) {
      contacts = firstPageResponse.data.contacts;
      cursor = firstPageResponse.data.cursor;
      hasMore = firstPageResponse.data.hasMore;
      console.log(`📈 Found ${contacts.length} contacts`);
      console.log(`🔄 Cursor: ${cursor ? 'Provided' : 'None'}`);
      console.log(`📄 Has more: ${hasMore}`);
    }
    
    results.all_contacts.push(...contacts);
    results.total_contacts_found = contacts.length;
    
    results.api_responses.push({
      page: 1,
      url: firstPageUrl,
      contacts_count: contacts.length,
      has_cursor: !!cursor,
      has_more: hasMore,
      response_type: Array.isArray(firstPageResponse.data) ? 'direct_array' : 'object_with_contacts'
    });

    // Show sample contact structure
    if (contacts.length > 0) {
      console.log(`📋 Sample contact fields:`, Object.keys(contacts[0]));
      results.sample_contact_fields = Object.keys(contacts[0]);
    }

    // Test 2: If we have more than 1000 contacts, test pagination
    if (contacts.length === 1000) {
      console.log('\n2️⃣ Testing second page (contacts 1001-2000)');
      
      const secondPageUrl = `${baseUrl}/encompass/v1/borrowerContactSelector?start=1001&limit=1000&cursorType=randomAccess`;

      try {
        const secondPageResponse = await axios.post(secondPageUrl, requestBody, { headers });
        
        console.log(`✅ Second page - Status: ${secondPageResponse.status}`);
        
        let secondPageContacts = [];
        if (Array.isArray(secondPageResponse.data)) {
          secondPageContacts = secondPageResponse.data;
        } else if (secondPageResponse.data && secondPageResponse.data.contacts) {
          secondPageContacts = secondPageResponse.data.contacts;
        }
        
        console.log(`📈 Found ${secondPageContacts.length} contacts on second page`);
        results.all_contacts.push(...secondPageContacts);
        results.total_contacts_found += secondPageContacts.length;
        
        results.api_responses.push({
          page: 2,
          url: secondPageUrl,
          contacts_count: secondPageContacts.length,
          response_type: Array.isArray(secondPageResponse.data) ? 'direct_array' : 'object_with_contacts'
        });
        
        results.pagination_details.pagination_working = true;
        results.pagination_details.total_pages_tested = 2;
        
      } catch (error) {
        console.error('❌ Error fetching second page:', error.response?.status, error.response?.data || error.message);
        results.errors.push({
          page: 2,
          error: error.response?.data || error.message,
          status: error.response?.status
        });
      }
    } else {
      console.log(`\n📊 Only ${contacts.length} contacts found, no second page needed`);
      results.pagination_details.pagination_working = false;
      results.pagination_details.total_pages_tested = 1;
    }

    // Test 3: Test smaller page size to verify pagination works
    console.log('\n3️⃣ Testing smaller page size (100 contacts per page)');
    
    const smallPageUrl = `${baseUrl}/encompass/v1/borrowerContactSelector?start=1&limit=100&cursorType=randomAccess`;

    try {
      const smallPageResponse = await axios.post(smallPageUrl, requestBody, { headers });
      
      console.log(`✅ Small page - Status: ${smallPageResponse.status}`);
      
      let smallPageContacts = [];
      if (Array.isArray(smallPageResponse.data)) {
        smallPageContacts = smallPageResponse.data;
      } else if (smallPageResponse.data && smallPageResponse.data.contacts) {
        smallPageContacts = smallPageResponse.data.contacts;
      }
      
      console.log(`📈 Found ${smallPageContacts.length} contacts with limit=100`);
      
      results.api_responses.push({
        page: 'small_test',
        url: smallPageUrl,
        contacts_count: smallPageContacts.length,
        limit_requested: 100,
        limit_respected: smallPageContacts.length <= 100
      });
      
      // Test next small page
      if (smallPageContacts.length === 100) {
        const nextSmallPageUrl = `${baseUrl}/encompass/v1/borrowerContactSelector?start=101&limit=100&cursorType=randomAccess`;
        
        const nextSmallPageResponse = await axios.post(nextSmallPageUrl, requestBody, { headers });
        
        let nextSmallPageContacts = [];
        if (Array.isArray(nextSmallPageResponse.data)) {
          nextSmallPageContacts = nextSmallPageResponse.data;
        } else if (nextSmallPageResponse.data && nextSmallPageResponse.data.contacts) {
          nextSmallPageContacts = nextSmallPageResponse.data.contacts;
        }
        
        console.log(`📈 Found ${nextSmallPageContacts.length} contacts on next small page`);
        
        results.pagination_details.small_page_pagination = {
          first_page: smallPageContacts.length,
          second_page: nextSmallPageContacts.length,
          working: true
        };
      }
      
    } catch (error) {
      console.error('❌ Error testing small page:', error.response?.status, error.response?.data || error.message);
      results.errors.push({
        page: 'small_test',
        error: error.response?.data || error.message,
        status: error.response?.status
      });
    }

  } catch (error) {
    console.error('❌ Error in main pagination test:', error.response?.status, error.response?.data || error.message);
    results.errors.push({
      page: 'main',
      error: error.response?.data || error.message,
      status: error.response?.status
    });
  }

  return results;
}

async function main() {
  try {
    const accessToken = await getAccessToken();
    const results = await testBorrowerContactsPagination(accessToken);
    
    // Save results
    const timestamp = Date.now();
    const filename = `data/borrower-contacts-pagination-proper-${timestamp}.json`;
    
    // Save summary without all contact data
    const summary = {
      ...results,
      all_contacts: `${results.all_contacts.length} contacts (truncated for file size)`
    };
    fs.writeFileSync(filename, JSON.stringify(summary, null, 2));
    
    // Save contacts separately if we have them
    if (results.all_contacts.length > 0) {
      const contactsFilename = `data/borrower-contacts-data-${timestamp}.json`;
      fs.writeFileSync(contactsFilename, JSON.stringify({
        timestamp: results.timestamp,
        total_contacts: results.total_contacts_found,
        contacts: results.all_contacts
      }, null, 2));
      console.log(`💾 Contacts data saved to: ${contactsFilename}`);
    }
    
    console.log('\n📊 PAGINATION TEST SUMMARY');
    console.log('='.repeat(50));
    console.log(`✅ Total contacts found: ${results.total_contacts_found}`);
    console.log(`📄 API responses tested: ${results.api_responses.length}`);
    console.log(`❌ Errors encountered: ${results.errors.length}`);
    console.log(`💾 Summary saved to: ${filename}`);
    
    if (results.pagination_details.pagination_working !== undefined) {
      console.log(`🔄 Pagination working: ${results.pagination_details.pagination_working ? '✅ YES' : '❌ NO'}`);
    }
    
    if (results.sample_contact_fields) {
      console.log(`📋 Contact fields available: ${results.sample_contact_fields.join(', ')}`);
    }
    
    if (results.errors.length > 0) {
      console.log('\n❌ ERRORS:');
      results.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. Page ${error.page}: ${error.status} - ${JSON.stringify(error.error)}`);
      });
    }
    
  } catch (error) {
    console.error('💥 Fatal error:', error.message);
    process.exit(1);
  }
}

// Run the test
main();
