const axios = require('axios');
const fs = require('fs');
require('dotenv').config();

// Configuration
const baseUrl = 'https://api.elliemae.com';
const username = process.env.ENCOMPASS_USERNAME;
const password = process.env.ENCOMPASS_PASSWORD;
const clientId = process.env.ENCOMPASS_CLIENT_ID;
const clientSecret = process.env.ENCOMPASS_CLIENT_SECRET;
const instanceId = process.env.ENCOMPASS_INSTANCE_ID;

console.log('🔍 Final Test: Borrower Contacts with Pagination');
console.log('='.repeat(60));

async function getAccessToken() {
  try {
    console.log('🔐 Getting access token...');
    
    const tokenResponse = await axios.post(`${baseUrl}/oauth2/v1/token`, 
      `grant_type=password&username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`,
      {
        headers: {
          'Authorization': `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`,
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );
    
    console.log('✅ Access token obtained successfully');
    return tokenResponse.data.access_token;
  } catch (error) {
    console.error('❌ Error getting access token:', error.response?.data || error.message);
    throw error;
  }
}

async function testBorrowerContacts(accessToken) {
  const headers = {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  };

  console.log('\n📋 Testing Borrower Contacts API');
  console.log('-'.repeat(50));

  const results = {
    timestamp: new Date().toISOString(),
    total_contacts_found: 0,
    all_contacts: [],
    tests_performed: [],
    errors: []
  };

  // Test 1: Simple approach without cursor
  console.log('\n1️⃣ Testing simple approach (no cursor)');
  try {
    const simplePayload = {
      start: 1,
      limit: 1000
    };

    const simpleResponse = await axios.post(
      `${baseUrl}/encompass/v1/borrowerContactSelector`,
      simplePayload,
      { headers }
    );

    console.log(`✅ Simple approach - Status: ${simpleResponse.status}`);
    
    let contacts = [];
    if (Array.isArray(simpleResponse.data)) {
      contacts = simpleResponse.data;
      console.log(`📈 Found ${contacts.length} contacts (direct array)`);
    } else if (simpleResponse.data && simpleResponse.data.contacts) {
      contacts = simpleResponse.data.contacts;
      console.log(`📈 Found ${contacts.length} contacts (object.contacts)`);
    } else {
      console.log(`📊 Response structure:`, Object.keys(simpleResponse.data || {}));
    }

    results.all_contacts = contacts;
    results.total_contacts_found = contacts.length;

    if (contacts.length > 0) {
      console.log(`📋 Sample contact fields:`, Object.keys(contacts[0]));
      
      results.tests_performed.push({
        test: 'Simple approach',
        success: true,
        contacts_found: contacts.length,
        sample_fields: Object.keys(contacts[0])
      });
    }

    // Test pagination with simple approach
    if (contacts.length === 1000) {
      console.log('\n📄 Testing second page (simple approach)');
      
      const secondPagePayload = {
        start: 1001,
        limit: 1000
      };

      const secondPageResponse = await axios.post(
        `${baseUrl}/encompass/v1/borrowerContactSelector`,
        secondPagePayload,
        { headers }
      );

      let secondPageContacts = [];
      if (Array.isArray(secondPageResponse.data)) {
        secondPageContacts = secondPageResponse.data;
      } else if (secondPageResponse.data && secondPageResponse.data.contacts) {
        secondPageContacts = secondPageResponse.data.contacts;
      }

      console.log(`📈 Second page: Found ${secondPageContacts.length} contacts`);
      
      results.all_contacts.push(...secondPageContacts);
      results.total_contacts_found += secondPageContacts.length;

      results.tests_performed.push({
        test: 'Simple pagination - page 2',
        success: true,
        contacts_found: secondPageContacts.length,
        pagination_working: secondPageContacts.length > 0
      });
    }

  } catch (error) {
    console.error('❌ Error with simple approach:', error.response?.status, error.response?.data || error.message);
    results.errors.push({
      test: 'Simple approach',
      error: error.response?.data || error.message,
      status: error.response?.status
    });
  }

  // Test 2: Try the old endpoint that worked before
  console.log('\n2️⃣ Testing old endpoint that worked');
  try {
    const oldPayload = {
      start: 0,
      limit: 100
    };

    const oldResponse = await axios.post(
      `${baseUrl}/encompass/v1/borrowerContactSelector/`,  // Note the trailing slash
      oldPayload,
      { headers }
    );

    console.log(`✅ Old endpoint - Status: ${oldResponse.status}`);
    
    let oldContacts = [];
    if (Array.isArray(oldResponse.data)) {
      oldContacts = oldResponse.data;
      console.log(`📈 Found ${oldContacts.length} contacts with old endpoint`);
    }

    results.tests_performed.push({
      test: 'Old endpoint with trailing slash',
      success: true,
      contacts_found: oldContacts.length
    });

  } catch (error) {
    console.error('❌ Error with old endpoint:', error.response?.status, error.response?.data || error.message);
    results.errors.push({
      test: 'Old endpoint',
      error: error.response?.data || error.message,
      status: error.response?.status
    });
  }

  // Test 3: Try with query parameters (as per documentation)
  console.log('\n3️⃣ Testing with query parameters');
  try {
    const queryUrl = `${baseUrl}/encompass/v1/borrowerContactSelector?start=1&limit=100`;

    const queryResponse = await axios.post(queryUrl, {}, { headers });

    console.log(`✅ Query params - Status: ${queryResponse.status}`);
    
    let queryContacts = [];
    if (Array.isArray(queryResponse.data)) {
      queryContacts = queryResponse.data;
      console.log(`📈 Found ${queryContacts.length} contacts with query params`);
    }

    results.tests_performed.push({
      test: 'Query parameters approach',
      success: true,
      contacts_found: queryContacts.length
    });

  } catch (error) {
    console.error('❌ Error with query params:', error.response?.status, error.response?.data || error.message);
    results.errors.push({
      test: 'Query parameters',
      error: error.response?.data || error.message,
      status: error.response?.status
    });
  }

  // Test 4: Try smaller batches to test pagination
  console.log('\n4️⃣ Testing small batch pagination');
  try {
    let allSmallBatchContacts = [];
    let currentStart = 1;
    let batchSize = 50;
    let maxBatches = 5;
    let batchCount = 0;

    while (batchCount < maxBatches) {
      console.log(`📄 Fetching batch ${batchCount + 1} (start: ${currentStart}, limit: ${batchSize})`);
      
      const batchPayload = {
        start: currentStart,
        limit: batchSize
      };

      const batchResponse = await axios.post(
        `${baseUrl}/encompass/v1/borrowerContactSelector`,
        batchPayload,
        { headers }
      );

      let batchContacts = [];
      if (Array.isArray(batchResponse.data)) {
        batchContacts = batchResponse.data;
      } else if (batchResponse.data && batchResponse.data.contacts) {
        batchContacts = batchResponse.data.contacts;
      }

      console.log(`   📈 Batch ${batchCount + 1}: ${batchContacts.length} contacts`);
      
      allSmallBatchContacts.push(...batchContacts);
      
      if (batchContacts.length < batchSize) {
        console.log(`   🏁 Last batch reached`);
        break;
      }
      
      currentStart += batchSize;
      batchCount++;
      
      // Small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    results.tests_performed.push({
      test: 'Small batch pagination',
      success: true,
      batches_fetched: batchCount + 1,
      total_contacts: allSmallBatchContacts.length,
      pagination_working: batchCount > 0
    });

    console.log(`✅ Small batch test: ${allSmallBatchContacts.length} total contacts in ${batchCount + 1} batches`);

  } catch (error) {
    console.error('❌ Error with small batch pagination:', error.response?.status, error.response?.data || error.message);
    results.errors.push({
      test: 'Small batch pagination',
      error: error.response?.data || error.message,
      status: error.response?.status
    });
  }

  return results;
}

async function main() {
  try {
    const accessToken = await getAccessToken();
    const results = await testBorrowerContacts(accessToken);
    
    // Save results
    const timestamp = Date.now();
    const filename = `data/borrower-contacts-final-test-${timestamp}.json`;
    
    // Save summary
    const summary = {
      ...results,
      all_contacts: `${results.all_contacts.length} contacts (truncated for file size)`
    };
    fs.writeFileSync(filename, JSON.stringify(summary, null, 2));
    
    // Save contacts data if we have them
    if (results.all_contacts.length > 0) {
      const contactsFilename = `data/borrower-contacts-final-data-${timestamp}.json`;
      fs.writeFileSync(contactsFilename, JSON.stringify({
        timestamp: results.timestamp,
        total_contacts: results.total_contacts_found,
        contacts: results.all_contacts
      }, null, 2));
      console.log(`💾 Contacts data saved to: ${contactsFilename}`);
    }
    
    console.log('\n📊 FINAL TEST SUMMARY');
    console.log('='.repeat(50));
    console.log(`✅ Total contacts found: ${results.total_contacts_found}`);
    console.log(`🧪 Tests performed: ${results.tests_performed.length}`);
    console.log(`❌ Errors encountered: ${results.errors.length}`);
    console.log(`💾 Summary saved to: ${filename}`);
    
    console.log('\n🧪 TEST RESULTS:');
    results.tests_performed.forEach((test, index) => {
      console.log(`   ${index + 1}. ${test.test}: ✅ ${test.contacts_found} contacts`);
      if (test.pagination_working !== undefined) {
        console.log(`      🔄 Pagination: ${test.pagination_working ? 'YES' : 'NO'}`);
      }
    });
    
    if (results.errors.length > 0) {
      console.log('\n❌ ERRORS:');
      results.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error.test}: ${error.status} - ${JSON.stringify(error.error)}`);
      });
    }
    
  } catch (error) {
    console.error('💥 Fatal error:', error.message);
    process.exit(1);
  }
}

// Run the comprehensive test
main();
