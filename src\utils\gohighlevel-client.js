const axios = require('axios');

/**
 * GoHighLevel API Client
 * Handles all interactions with GoHighLevel API
 */
class GoHighLevelClient {
    constructor(config) {
        this.baseUrl = 'https://services.leadconnectorhq.com';
        this.apiKey = config.apiKey || process.env.GOHIGHLEVEL_API_KEY;
        this.locationId = config.locationId || process.env.GOHIGHLEVEL_LOCATION_ID;
        this.pipelineId = config.pipelineId || process.env.GOHIGHLEVEL_PIPELINE_ID;
        this.pipelineStageId = config.pipelineStageId || process.env.GOHIGHLEVEL_PIPELINE_STAGE_ID;
        this.apiCallCount = 0;
    }

    /**
     * Make API call to GoHighLevel
     * @param {string} endpoint - API endpoint
     * @param {object} options - Request options
     * @param {string} description - Description for logging
     * @returns {Promise<any>} API response data
     */
    async makeApiCall(endpoint, options = {}, description = 'API call') {
        const url = `${this.baseUrl}${endpoint}`;
        
        const config = {
            ...options,
            headers: {
                'Accept': 'application/json',
                'Authorization': `Bearer ${this.apiKey}`,
                'Content-Type': 'application/json',
                'Version': '2021-07-28',
                ...options.headers
            }
        };

        try {
            this.apiCallCount++;
            console.log(`[${new Date().toISOString()}] 🌐 API Call ${this.apiCallCount}: ${description}`);
            
            const response = await axios(url, config);
            
            // Rate limiting
            await new Promise(resolve => setTimeout(resolve, 200));
            
            return response.data;
        } catch (error) {
            console.error(`[${new Date().toISOString()}] ❌ ${description} failed:`, error.response?.data || error.message);
            throw error;
        }
    }

    /**
     * Search for duplicate contacts by email
     * @param {string} email - Email to search for
     * @returns {Promise<string|null>} Contact ID if found, null otherwise
     */
    async searchContactByEmail(email) {
        if (!email || !email.trim()) {
            return null;
        }

        try {
            const encodedEmail = encodeURIComponent(email.trim())
                .replace(/\+/g, '%2B')
                .replace(/@/g, '%40');

            const url = `/contacts/search/duplicate?locationId=${this.locationId}&email=${encodedEmail}`;
            
            const response = await this.makeApiCall(url, { method: 'GET' }, `Checking duplicate by email: ${email}`);
            
            if (response && response.contact && response.contact.id) {
                console.log(`   ✅ Found duplicate contact by email with ID: ${response.contact.id}`);
                return response.contact.id;
            }
            
            return null;
        } catch (error) {
            console.error(`   ❌ Error checking for duplicate contact:`, error.response?.data || error.message);
            return null;
        }
    }

    /**
     * Create a new contact in GoHighLevel
     * @param {object} contactData - Contact data
     * @returns {Promise<object>} Created contact response
     */
    async createContact(contactData) {
        const data = {
            locationId: this.locationId,
            ...contactData
        };

        const response = await this.makeApiCall(
            '/contacts/',
            {
                method: 'POST',
                data
            },
            `Creating new GHL contact: ${contactData.firstName} ${contactData.lastName}`
        );

        return {
            success: true,
            contactId: response.contact?.id,
            action: 'created'
        };
    }

    /**
     * Update an existing contact in GoHighLevel
     * @param {string} contactId - Contact ID to update
     * @param {object} contactData - Contact data
     * @returns {Promise<object>} Updated contact response
     */
    async updateContact(contactId, contactData) {
        // Remove locationId for updates
        const { locationId, ...updateData } = contactData;

        const response = await this.makeApiCall(
            `/contacts/${contactId}`,
            {
                method: 'PUT',
                data: updateData
            },
            `Updating existing GHL contact: ${contactData.firstName} ${contactData.lastName}`
        );

        return {
            success: true,
            contactId,
            action: 'updated'
        };
    }

    /**
     * Create or update a contact
     * @param {object} contactData - Contact data
     * @returns {Promise<object>} Contact operation result
     */
    async createOrUpdateContact(contactData) {
        try {
            // Check for duplicate by email
            const existingContactId = await this.searchContactByEmail(contactData.email);

            if (existingContactId) {
                return await this.updateContact(existingContactId, contactData);
            } else {
                return await this.createContact(contactData);
            }
        } catch (error) {
            console.error(`❌ Error creating/updating GHL contact:`, error.response?.data || error.message);
            throw error;
        }
    }

    /**
     * Create an opportunity in GoHighLevel
     * @param {string} contactId - Contact ID
     * @param {object} contactData - Contact data for opportunity details
     * @returns {Promise<object>} Opportunity creation result
     */
    async createOpportunity(contactId, contactData) {
        if (!contactId) {
            throw new Error('contactId must be provided to create an opportunity');
        }

        if (!this.pipelineId || !this.pipelineStageId) {
            console.log(`   ℹ️ Opportunity creation skipped (missing pipeline configuration)`);
            return { success: false, message: 'Missing pipeline configuration' };
        }

        try {
            const opportunityData = {
                pipelineId: this.pipelineId,
                pipelineStageId: this.pipelineStageId,
                locationId: this.locationId,
                name: `${contactData.firstName || ''} ${contactData.lastName || ''} - Mortgage Lead`.trim(),
                status: "open",
                contactId,
                monetaryValue: 0,
                customFields: [
                    { key: 'source', field_value: 'Encompass API' },
                    { key: 'lead_type', field_value: 'Mortgage Borrower' },
                    { key: 'import_date', field_value: new Date().toISOString() }
                ].filter(field => field.field_value !== '')
            };

            const response = await this.makeApiCall(
                '/opportunities/',
                {
                    method: 'POST',
                    data: opportunityData
                },
                `Creating opportunity for ${contactData.firstName} ${contactData.lastName}`
            );

            const opportunityId = response?.opportunity?.id;
            console.log(`   ✅ Created new opportunity with ID: ${opportunityId}`);

            return {
                success: true,
                opportunityId,
                isNew: true,
                message: 'New opportunity created'
            };

        } catch (error) {
            const errorData = error.response?.data;
            
            // Handle duplicate opportunity error
            if (errorData?.statusCode === 400 && (errorData?.message?.includes('duplicate') || errorData?.message?.includes('Can not create duplicate'))) {
                console.log(`   ℹ️ Duplicate opportunity detected, using existing one`);
                return {
                    success: true,
                    opportunityId: 'existing',
                    isNew: false,
                    message: 'Duplicate opportunity detected'
                };
            }

            console.error(`❌ Failed to create opportunity:`, errorData || error.message);
            throw error;
        }
    }
}

module.exports = GoHighLevelClient;
