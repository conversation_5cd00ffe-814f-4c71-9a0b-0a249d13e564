#!/usr/bin/env node

/**
 * Cleanup Legacy Files Script
 * Removes scattered legacy files and organizes the codebase
 */

const fs = require('fs');
const path = require('path');

// Files to be removed (legacy/scattered files)
const filesToRemove = [
    // Legacy integration files
    'test-connection.js',
    'test-encompass-integration.js',
    'test-encompass-data.js',
    'test-encompass-permissions.js',
    'test-users-and-leads.js',
    'test-accessible-data.js',
    'fetch-all-encompass-data.js',
    'fetch-borrower-contacts-paginated.js',
    'test-enhanced-features.js',
    'setup-encompass-webhooks.js',
    'encompass-webhook-integration.js',
    'index.js',
    
    // Legacy token manager (functionality moved to src/utils/encompass-auth.js)
    'encompass-token-manager.js',
    
    // Legacy database files (not needed for current implementation)
    'database.js',
    'database.sqlite',
    
    // Legacy test files
    'test-ghl-api.js',
    'test-ghl-integration.js',
    
    // Other scattered files
    'webhook-test.js',
    'manual-sync.js'
];

// Files to keep but note as legacy
const legacyFiles = [
    'encompass-to-gohighlevel-integration.js' // Keep as legacy reference
];

function cleanupFiles() {
    console.log('🧹 Cleaning up legacy and scattered files...');
    console.log('='.repeat(60));

    let removedCount = 0;
    let keptCount = 0;
    let notFoundCount = 0;

    filesToRemove.forEach(filename => {
        const filePath = path.join(__dirname, filename);
        
        if (fs.existsSync(filePath)) {
            try {
                // Check if it's a directory
                const stats = fs.statSync(filePath);
                if (stats.isDirectory()) {
                    console.log(`📁 Skipping directory: ${filename}`);
                    return;
                }

                // Remove the file
                fs.unlinkSync(filePath);
                console.log(`🗑️  Removed: ${filename}`);
                removedCount++;
            } catch (error) {
                console.error(`❌ Error removing ${filename}:`, error.message);
            }
        } else {
            console.log(`ℹ️  Not found: ${filename}`);
            notFoundCount++;
        }
    });

    // Check legacy files
    legacyFiles.forEach(filename => {
        const filePath = path.join(__dirname, filename);
        if (fs.existsSync(filePath)) {
            console.log(`📜 Kept as legacy: ${filename}`);
            keptCount++;
        }
    });

    console.log('\n📊 Cleanup Summary:');
    console.log('='.repeat(60));
    console.log(`🗑️  Files removed: ${removedCount}`);
    console.log(`📜 Legacy files kept: ${keptCount}`);
    console.log(`ℹ️  Files not found: ${notFoundCount}`);
    console.log(`✅ Total processed: ${filesToRemove.length}`);
}

function updatePackageJson() {
    console.log('\n📝 Updating package.json scripts...');
    console.log('='.repeat(60));

    const packagePath = path.join(__dirname, 'package.json');
    
    try {
        const packageData = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
        
        // Remove legacy scripts
        const scriptsToRemove = [
            'test-integration',
            'test-encompass',
            'test-permissions',
            'test-users',
            'test-data',
            'fetch-all',
            'fetch-contacts',
            'test-enhanced',
            'sync-once',
            'setup-webhooks',
            'webhook-list',
            'webhook-resources',
            'webhook-server'
        ];

        let removedScripts = 0;
        scriptsToRemove.forEach(script => {
            if (packageData.scripts[script]) {
                delete packageData.scripts[script];
                removedScripts++;
                console.log(`🗑️  Removed script: ${script}`);
            }
        });

        // Update main entry point
        packageData.main = 'server.js';
        packageData.version = '2.0.0';
        packageData.description = 'Enhanced Encompass to GoHighLevel integration with webhook support and comprehensive dashboard';

        // Clean up scripts object
        const cleanScripts = {
            "start": "node server.js",
            "dev": "nodemon server.js",
            "legacy": "node encompass-to-gohighlevel-integration.js",
            "integration": "node src/integration-service.js",
            "webhook": "node src/webhook-server.js",
            "borrower-count": "node src/scripts/get-borrower-count.js",
            "test-comprehensive": "node src/scripts/test-comprehensive-extraction.js",
            "setup-webhook": "node src/scripts/setup-webhook.js",
            "token-info": "node encompass-token-manager.js info",
            "token-refresh": "node encompass-token-manager.js refresh",
            "test": "echo \"Error: no test specified\" && exit 1"
        };

        packageData.scripts = cleanScripts;

        // Write updated package.json
        fs.writeFileSync(packagePath, JSON.stringify(packageData, null, 2));
        
        console.log(`✅ Updated package.json`);
        console.log(`📝 Removed ${removedScripts} legacy scripts`);
        console.log(`🎯 Updated main entry point to: ${packageData.main}`);
        console.log(`📦 Updated version to: ${packageData.version}`);

    } catch (error) {
        console.error('❌ Error updating package.json:', error.message);
    }
}

function createLegacyFolder() {
    console.log('\n📁 Creating legacy folder structure...');
    console.log('='.repeat(60));

    const legacyDir = path.join(__dirname, 'legacy');
    
    if (!fs.existsSync(legacyDir)) {
        fs.mkdirSync(legacyDir);
        console.log('✅ Created legacy/ directory');
    }

    // Move the main legacy integration file
    const legacyIntegrationFile = 'encompass-to-gohighlevel-integration.js';
    const sourcePath = path.join(__dirname, legacyIntegrationFile);
    const destPath = path.join(legacyDir, legacyIntegrationFile);

    if (fs.existsSync(sourcePath)) {
        try {
            fs.copyFileSync(sourcePath, destPath);
            console.log(`📜 Copied ${legacyIntegrationFile} to legacy/`);
        } catch (error) {
            console.error(`❌ Error copying ${legacyIntegrationFile}:`, error.message);
        }
    }

    // Create a README for the legacy folder
    const legacyReadme = `# Legacy Files

This folder contains legacy integration files that are kept for reference.

## Files

- \`encompass-to-gohighlevel-integration.js\` - Original integration script

## Usage

To run the legacy integration:

\`\`\`bash
npm run legacy
\`\`\`

## Migration

The functionality from these files has been reorganized into:

- \`src/\` - Organized source code
- \`server.js\` - Comprehensive server with dashboard
- \`src/services/\` - Service classes
- \`src/utils/\` - Utility classes
- \`src/scripts/\` - Standalone scripts

Use the new organized structure for all new development.
`;

    fs.writeFileSync(path.join(legacyDir, 'README.md'), legacyReadme);
    console.log('✅ Created legacy/README.md');
}

function displayNewStructure() {
    console.log('\n🏗️  New Project Structure:');
    console.log('='.repeat(60));
    console.log(`
📁 Project Root
├── 🚀 server.js                          # Main comprehensive server
├── 📋 package.json                       # Updated dependencies & scripts
├── 🔧 .env                              # Environment configuration
├── 📖 README.md                         # Project documentation
├── 📁 src/                              # Organized source code
│   ├── 📁 utils/
│   │   ├── encompass-auth.js            # Encompass authentication
│   │   └── gohighlevel-client.js        # GoHighLevel API client
│   ├── 📁 services/
│   │   ├── borrower-service.js          # Borrower operations
│   │   └── webhook-service.js           # Webhook handling
│   ├── 📁 scripts/
│   │   ├── get-borrower-count.js        # Get total borrower count
│   │   ├── test-comprehensive-extraction.js  # Test data extraction
│   │   └── setup-webhook.js             # Webhook setup utility
│   ├── integration-service.js           # Enhanced batch integration
│   └── webhook-server.js                # Standalone webhook server
├── 📁 data/                             # Integration results & logs
├── 📁 legacy/                           # Legacy files (reference only)
│   ├── encompass-to-gohighlevel-integration.js
│   └── README.md
└── 📁 node_modules/                     # Dependencies
`);

    console.log('\n🎯 Quick Start Commands:');
    console.log('='.repeat(60));
    console.log('🚀 Start comprehensive server:  npm start');
    console.log('🔧 Development mode:            npm run dev');
    console.log('📊 Get borrower count:          npm run borrower-count');
    console.log('🔄 Run batch integration:       npm run integration');
    console.log('📡 Setup webhooks:              npm run setup-webhook');
    console.log('📜 Run legacy integration:      npm run legacy');
    console.log('');
    console.log('🌐 Access dashboard at: http://localhost:3000');
}

// Main execution
function main() {
    console.log('🧹 Encompass to GoHighLevel Integration Cleanup');
    console.log('='.repeat(60));
    console.log('This script will clean up scattered legacy files and organize the codebase.');
    console.log('');

    // Perform cleanup operations
    cleanupFiles();
    updatePackageJson();
    createLegacyFolder();
    displayNewStructure();

    console.log('\n✅ Cleanup completed successfully!');
    console.log('🎉 Your codebase is now organized and ready for production use.');
    console.log('');
    console.log('Next steps:');
    console.log('1. Run "npm start" to launch the comprehensive server');
    console.log('2. Open http://localhost:3000 to access the dashboard');
    console.log('3. Configure webhooks using "npm run setup-webhook"');
    console.log('4. Test the integration with the dashboard interface');
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = { cleanupFiles, updatePackageJson, createLegacyFolder };
