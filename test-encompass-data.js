const EncompassApi = require('./encompassApi');
const logger = require('./logger');
const fs = require('fs');
const path = require('path');

async function testEncompassApi() {
  console.log('🔍 Testing Encompass API and Examining Lead Data...\n');

  try {
    // Initialize Encompass API
    const encompassApi = new EncompassApi();
    
    console.log('1. Testing Encompass API Authentication...');
    
    // Test authentication
    const authResult = await encompassApi.authenticate();
    if (!authResult) {
      throw new Error('Failed to authenticate with Encompass API');
    }
    console.log('✅ Successfully authenticated with Encompass API\n');

    console.log('2. Fetching recent leads from Encompass...');
    
    // Fetch recent leads (last 30 days to get more data)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const leads = await encompassApi.getRecentLeads(thirtyDaysAgo.toISOString());
    
    console.log(`📊 Found ${leads.length} leads in the last 30 days\n`);

    if (leads.length === 0) {
      console.log('⚠️  No leads found. This could mean:');
      console.log('   - No recent loan applications');
      console.log('   - API permissions may be limited');
      console.log('   - Date range may need adjustment');
      console.log('   - Field access permissions may be restricted\n');
      return;
    }

    console.log('3. Analyzing lead data structure...\n');

    // Analyze first few leads
    const samplesToAnalyze = Math.min(3, leads.length);
    
    for (let i = 0; i < samplesToAnalyze; i++) {
      const lead = leads[i];
      console.log(`--- Lead ${i + 1} Analysis ---`);
      console.log('Raw lead object keys:', Object.keys(lead));
      console.log('Lead data sample:');
      console.log(JSON.stringify(lead, null, 2));
      console.log('\n');
    }

    // Save raw data for analysis
    const dataDir = path.join(__dirname, 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    const rawDataFile = path.join(dataDir, 'encompass-raw-data.json');
    const analysisData = {
      timestamp: new Date().toISOString(),
      totalLeads: leads.length,
      sampleLeads: leads.slice(0, 5), // Save first 5 for analysis
      fieldAnalysis: analyzeFields(leads)
    };

    fs.writeFileSync(rawDataFile, JSON.stringify(analysisData, null, 2));
    console.log(`💾 Raw data saved to: ${rawDataFile}\n`);

    console.log('4. Field Analysis Summary:');
    console.log('========================');
    
    const fieldStats = analyzeFields(leads);
    Object.entries(fieldStats).forEach(([field, stats]) => {
      console.log(`${field}:`);
      console.log(`  - Present in ${stats.count}/${leads.length} leads (${Math.round(stats.count/leads.length*100)}%)`);
      console.log(`  - Sample values: ${stats.sampleValues.slice(0, 3).join(', ')}`);
      console.log(`  - Data types: ${[...stats.dataTypes].join(', ')}`);
      console.log('');
    });

    console.log('5. Recommended Field Mapping:');
    console.log('=============================');
    
    const recommendations = generateFieldMappingRecommendations(fieldStats);
    recommendations.forEach(rec => {
      console.log(`${rec.ghlField} ← ${rec.encompassField} (${rec.confidence}% confidence)`);
      if (rec.notes) console.log(`  Notes: ${rec.notes}`);
    });

    console.log('\n6. Data Quality Assessment:');
    console.log('===========================');
    
    const qualityReport = assessDataQuality(leads);
    console.log(`Complete records (name + contact): ${qualityReport.completeRecords}/${leads.length}`);
    console.log(`Records with email: ${qualityReport.withEmail}/${leads.length}`);
    console.log(`Records with phone: ${qualityReport.withPhone}/${leads.length}`);
    console.log(`Records needing generated email: ${qualityReport.needGeneratedEmail}`);
    console.log(`Records needing generated phone: ${qualityReport.needGeneratedPhone}`);

    console.log('\n🎉 Encompass API test completed successfully!');
    console.log(`\n📋 Next Steps:`);
    console.log(`1. Review the raw data file: ${rawDataFile}`);
    console.log(`2. Adjust field mappings in encompassApi.js if needed`);
    console.log(`3. Test the full integration with: npm run test`);

  } catch (error) {
    console.error('❌ Encompass API test failed:', error.message);
    console.error('\n🔧 Troubleshooting:');
    console.error('1. Check your .env file has correct Encompass credentials');
    console.error('2. Verify your Encompass user has API access permissions');
    console.error('3. Ensure your Instance ID is correct');
    console.error('4. Check if your IP is whitelisted for API access');
    console.error('\nFull error details:');
    console.error(error);
    process.exit(1);
  }
}

function analyzeFields(leads) {
  const fieldStats = {};
  
  leads.forEach(lead => {
    Object.entries(lead).forEach(([field, value]) => {
      if (!fieldStats[field]) {
        fieldStats[field] = {
          count: 0,
          sampleValues: [],
          dataTypes: new Set()
        };
      }
      
      if (value !== null && value !== undefined && value !== '') {
        fieldStats[field].count++;
        fieldStats[field].dataTypes.add(typeof value);
        
        if (fieldStats[field].sampleValues.length < 5) {
          fieldStats[field].sampleValues.push(String(value).substring(0, 50));
        }
      }
    });
  });
  
  return fieldStats;
}

function generateFieldMappingRecommendations(fieldStats) {
  const recommendations = [];
  
  // Common field mappings to look for
  const mappings = [
    { ghl: 'firstName', patterns: ['firstname', 'first_name', 'fname', 'borrower_first'], confidence: 90 },
    { ghl: 'lastName', patterns: ['lastname', 'last_name', 'lname', 'borrower_last'], confidence: 90 },
    { ghl: 'email', patterns: ['email', 'email_address', 'borrower_email'], confidence: 95 },
    { ghl: 'phone', patterns: ['phone', 'phone_number', 'borrower_phone', 'mobile'], confidence: 90 },
    { ghl: 'address', patterns: ['address', 'street', 'property_address', 'subject_property'], confidence: 80 },
    { ghl: 'city', patterns: ['city', 'property_city'], confidence: 85 },
    { ghl: 'state', patterns: ['state', 'property_state'], confidence: 85 },
    { ghl: 'zipCode', patterns: ['zip', 'zipcode', 'postal_code', 'property_zip'], confidence: 85 },
    { ghl: 'loanAmount', patterns: ['loan_amount', 'amount', 'loan_value', 'purchase_price'], confidence: 75 }
  ];
  
  mappings.forEach(mapping => {
    const matchedFields = Object.keys(fieldStats).filter(field => 
      mapping.patterns.some(pattern => 
        field.toLowerCase().includes(pattern.toLowerCase())
      )
    );
    
    matchedFields.forEach(field => {
      recommendations.push({
        ghlField: mapping.ghl,
        encompassField: field,
        confidence: mapping.confidence,
        notes: `Found ${fieldStats[field].count} records with this field`
      });
    });
  });
  
  return recommendations.sort((a, b) => b.confidence - a.confidence);
}

function assessDataQuality(leads) {
  let completeRecords = 0;
  let withEmail = 0;
  let withPhone = 0;
  let needGeneratedEmail = 0;
  let needGeneratedPhone = 0;
  
  leads.forEach(lead => {
    const hasName = (lead.firstName || lead.first_name) && (lead.lastName || lead.last_name);
    const hasEmail = lead.email && lead.email.includes('@');
    const hasPhone = lead.phone && lead.phone.length >= 10;
    
    if (hasName && (hasEmail || hasPhone)) completeRecords++;
    if (hasEmail) withEmail++;
    if (hasPhone) withPhone++;
    if (hasName && !hasEmail) needGeneratedEmail++;
    if (hasName && !hasPhone) needGeneratedPhone++;
  });
  
  return {
    completeRecords,
    withEmail,
    withPhone,
    needGeneratedEmail,
    needGeneratedPhone
  };
}

// Run test if this file is executed directly
if (require.main === module) {
  testEncompassApi()
    .then(() => {
      console.log('\n✅ Test completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testEncompassApi };
