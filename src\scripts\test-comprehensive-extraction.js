#!/usr/bin/env node

/**
 * Test Comprehensive Data Extraction Script
 * Tests the comprehensive data extraction functionality
 */

require('dotenv').config();
const BorrowerService = require('../services/borrower-service');
const fs = require('fs');
const path = require('path');

async function main() {
    console.log('🚀 Encompass Comprehensive Data Extraction Test');
    console.log('='.repeat(60));

    try {
        // Initialize borrower service
        const borrowerService = new BorrowerService();

        // Get sample borrower contacts
        const sampleLimit = parseInt(process.argv[2]) || 5;
        console.log(`📥 Fetching ${sampleLimit} sample borrower contacts...`);

        const contactsResponse = await borrowerService.getBorrowerContactIds(sampleLimit);
        console.log(`✅ Retrieved ${contactsResponse.contactIds.length} contact IDs`);

        const comprehensiveRecords = [];
        let processedCount = 0;

        for (const contactId of contactsResponse.contactIds) {
            try {
                processedCount++;
                console.log(`\n📄 Processing contact ${processedCount}/${contactsResponse.contactIds.length}: ${contactId}`);

                // Get detailed contact information
                const borrowerDetails = await borrowerService.getBorrowerContactDetails(contactId);
                console.log(`   ✅ Retrieved: ${borrowerDetails.firstName} ${borrowerDetails.lastName}`);

                // Extract comprehensive data
                const comprehensiveRecord = borrowerService.extractComprehensiveData(
                    borrowerDetails,
                    null, // No loan data for now
                    null  // No loan associates for now
                );

                comprehensiveRecords.push(comprehensiveRecord);

                // Display summary
                console.log(`   📧 Email: ${comprehensiveRecord.borrower.email || 'N/A'}`);
                console.log(`   📞 Phone: ${comprehensiveRecord.borrower.phones.home || comprehensiveRecord.borrower.phones.mobile || 'N/A'}`);
                console.log(`   🏠 Address: ${comprehensiveRecord.borrower.address?.city || 'N/A'}, ${comprehensiveRecord.borrower.address?.state || 'N/A'}`);
                console.log(`   💼 Employer: ${comprehensiveRecord.borrower.employer?.name || 'N/A'}`);
                console.log(`   👥 Referral: ${comprehensiveRecord.realtor.name || 'N/A'}`);

            } catch (error) {
                console.error(`   ❌ Error processing contact ${contactId}:`, error.message);
            }
        }

        // Generate summary report
        console.log('\n📊 Comprehensive Data Extraction Summary');
        console.log('='.repeat(60));
        console.log(`📥 Total contacts processed: ${comprehensiveRecords.length}`);
        console.log(`✅ Successful extractions: ${comprehensiveRecords.length}`);
        console.log(`❌ Failed extractions: ${processedCount - comprehensiveRecords.length}`);

        // Analyze data completeness
        const dataCompleteness = analyzeDataCompleteness(comprehensiveRecords);
        console.log('\n📈 Data Completeness Analysis:');
        console.log('='.repeat(60));
        Object.entries(dataCompleteness).forEach(([field, percentage]) => {
            const bar = '█'.repeat(Math.round(percentage / 5)) + '░'.repeat(20 - Math.round(percentage / 5));
            console.log(`   ${field.padEnd(20)}: ${bar} ${percentage.toFixed(1)}%`);
        });

        // Save results to file
        const resultsFile = path.join(__dirname, '../../data', `comprehensive-extraction-${Date.now()}.json`);
        
        // Ensure data directory exists
        const dataDir = path.dirname(resultsFile);
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }

        fs.writeFileSync(resultsFile, JSON.stringify({
            summary: {
                totalProcessed: comprehensiveRecords.length,
                extractedAt: new Date().toISOString(),
                dataCompleteness
            },
            records: comprehensiveRecords
        }, null, 2));

        console.log(`\n💾 Results saved to: ${resultsFile}`);
        console.log('\n✅ Comprehensive data extraction test completed successfully!');

    } catch (error) {
        console.error('❌ Error in comprehensive data extraction test:', error.message);
        process.exit(1);
    }
}

/**
 * Analyze data completeness across all records
 * @param {Array} records - Array of comprehensive records
 * @returns {Object} Data completeness percentages
 */
function analyzeDataCompleteness(records) {
    if (records.length === 0) return {};

    const fields = {
        'First Name': (r) => !!r.borrower.firstName,
        'Last Name': (r) => !!r.borrower.lastName,
        'Email': (r) => !!r.borrower.email,
        'Home Phone': (r) => !!r.borrower.phones.home,
        'Mobile Phone': (r) => !!r.borrower.phones.mobile,
        'Work Phone': (r) => !!r.borrower.phones.work,
        'Date of Birth': (r) => !!r.borrower.dateOfBirth,
        'Address': (r) => !!r.borrower.address?.street1,
        'City': (r) => !!r.borrower.address?.city,
        'State': (r) => !!r.borrower.address?.state,
        'Zip Code': (r) => !!r.borrower.address?.zip,
        'Employer': (r) => !!r.borrower.employer?.name,
        'Job Title': (r) => !!r.borrower.employer?.jobTitle,
        'Referral/Realtor': (r) => !!r.realtor.name
    };

    const completeness = {};
    
    Object.entries(fields).forEach(([fieldName, checkFunction]) => {
        const completeCount = records.filter(checkFunction).length;
        completeness[fieldName] = (completeCount / records.length) * 100;
    });

    return completeness;
}

// Run the script
if (require.main === module) {
    main();
}

module.exports = main;
