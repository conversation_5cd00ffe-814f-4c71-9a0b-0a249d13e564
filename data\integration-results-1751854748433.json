{"summary": {"totalProcessed": 10, "created": 1, "updated": 9, "errors": 1, "apiCalls": 21, "elapsedTime": 24.178}, "processedContacts": [{"encompassId": "a26af9f3-2d41-4ae1-9a41-366437ff8aaa", "ghlContactId": "ieJuapqR19A22spHPIij", "action": "updated", "name": "BARBARA HERRERA VEGA", "email": "<EMAIL>", "phone": "************"}, {"encompassId": "7daa9d4a-5411-4a47-8650-0bf2946a5a4d", "ghlContactId": "F7HQZG4VBTV2tZlruFZp", "action": "updated", "name": "ORLANDO GIL", "email": "<EMAIL>", "phone": "************"}, {"encompassId": "95671b80-e118-4aa8-aaf9-cd55db29dbff", "ghlContactId": "LN2gHsOmq9inhCkQkLdr", "action": "updated", "name": "ARIANA RODRIGUEZ", "email": "<EMAIL>", "phone": "************"}, {"encompassId": "609bf4b4-c530-4d0f-bfa5-c5278fc5173d", "ghlContactId": "AV9SqXkAjsFVq6p1PCrl", "action": "updated", "name": "MANUEL JUANES", "email": "<EMAIL>", "phone": "************"}, {"encompassId": "380a9e11-7207-410c-b5a7-d9277439cccf", "ghlContactId": "c25F3EGWhia0AEJ3NYZy", "action": "updated", "name": "TONY MEDEROS", "email": "<EMAIL>", "phone": "************"}, {"encompassId": "ac709e4c-2a38-4b93-bcf2-6c6df13245dd", "ghlContactId": "fC0b5Q9m2RBfaMdUQAPj", "action": "updated", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "************"}, {"encompassId": "773d01eb-c5e8-43a2-a5a6-028bec114ce9", "ghlContactId": "PNZ9T5RKEAfcFl5qmhIO", "action": "updated", "name": "<PERSON>", "email": "<EMAIL>", "phone": "************"}, {"encompassId": "1142bbe1-b4f9-4c44-b757-eb6895748024", "action": "created", "name": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "************"}, {"encompassId": "d0323134-23a6-4b44-902f-52429fd6dc33", "ghlContactId": "0bHzj66hwvbiyHuOkYPr", "action": "updated", "name": "<PERSON>", "email": "<EMAIL>", "phone": "************"}, {"encompassId": "e0c19257-b1d0-407a-bb63-652d647881d4", "ghlContactId": "0bHzj66hwvbiyHuOkYPr", "action": "updated", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "************"}], "errors": [{"description": "Creating new GHL contact: <PERSON><PERSON>", "error": {"statusCode": 400, "message": "This location does not allow duplicated contacts.", "meta": {"contactId": "PNZ9T5RKEAfcFl5qmhIO", "matchingField": "phone"}, "traceId": "4d3f7ff2-1c12-411f-a12d-c3b48f384a74"}}]}