#!/usr/bin/env node

/**
 * Test External User Webhook
 * 
 * This script simulates an Encompass External User webhook notification
 * to test the webhook processing functionality.
 */

const axios = require('axios');

// Sample External User webhook payload based on Encompass documentation
const sampleWebhookPayload = {
    "eventId": "b7cb3e9c-2481-4c9c-af90-55a5d1b6a693",
    "eventTime": "2025-07-16T21:00:00.000Z",
    "eventType": "create",
    "meta": {
        "userId": "admin",
        "resourceType": "ExternalUsers",
        "resourceId": "423253b1-cd23-4424-b6e8-204dfce2751e",
        "instanceId": "BE11140034",
        "resourceRef": "/encompass/v3/externalUsers",
        "payload": {
            "entities": [
                {
                    "id": "test-borrower-001"
                }
            ]
        }
    }
};

async function testWebhook() {
    try {
        console.log('🧪 Testing External User Webhook...');
        console.log('📤 Sending webhook payload to http://localhost:3000/webhook/encompass');
        console.log('📋 Payload:', JSON.stringify(sampleWebhookPayload, null, 2));

        const response = await axios.post('http://localhost:3000/webhook/encompass', sampleWebhookPayload, {
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        console.log('✅ Webhook sent successfully!');
        console.log('📨 Response:', response.data);
        console.log('🔍 Check server logs for processing details');
        
    } catch (error) {
        console.error('❌ Error sending webhook:', error.response?.data || error.message);
        
        if (error.code === 'ECONNREFUSED') {
            console.log('💡 Make sure the server is running: npm run dev');
        }
    }
}

// Run the test
testWebhook();
