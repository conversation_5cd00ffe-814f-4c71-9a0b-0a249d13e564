const axios = require('axios');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

console.log('🧪 Encompass Contact Fetcher - 100 Full Contact Details');
console.log('='.repeat(60));

// Configuration
const baseUrl = 'https://api.elliemae.com';
const username = process.env.ENCOMPASS_USERNAME;
const password = process.env.ENCOMPASS_PASSWORD;
const clientId = process.env.ENCOMPASS_CLIENT_ID;
const clientSecret = process.env.ENCOMPASS_CLIENT_SECRET;

// Ensure data directory exists
const dataDir = path.join(__dirname, 'data');
if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
}

// Authentication function (borrowed from working files)
async function getAccessToken() {
    try {
        console.log('🔐 Getting access token...');

        const tokenResponse = await axios.post(`${baseUrl}/oauth2/v1/token`,
            `grant_type=password&username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`,
            {
                headers: {
                    'Authorization': `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`,
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            }
        );

        console.log('✅ Access token obtained successfully');
        return tokenResponse.data.access_token;
    } catch (error) {
        console.error('❌ Error getting access token:', error.response?.data || error.message);
        throw error;
    }
}

class ContactFetcher {
    constructor() {
        this.fetchedContacts = [];
        this.startTime = Date.now();
    }

    // Save contacts to JSON file
    saveContactsToFile(contacts, filename) {
        try {
            const timestamp = Date.now();
            const fullFilename = filename || `encompass-contacts-${timestamp}.json`;
            const filepath = path.join(dataDir, fullFilename);

            const dataToSave = {
                metadata: {
                    totalContacts: contacts.length,
                    fetchedAt: new Date().toISOString(),
                    elapsedTime: (Date.now() - this.startTime) / 100,
                    source: 'Encompass API'
                },
                contacts: contacts
            };

            fs.writeFileSync(filepath, JSON.stringify(dataToSave, null, 2));
            console.log(`💾 Contacts saved to: ${fullFilename}`);
            console.log(`📁 Full path: ${filepath}`);
            return filepath;
        } catch (error) {
            console.error('❌ Error saving contacts:', error.message);
            return null;
        }
    }

    // Get total count of borrower contacts
    async getTotalContactCount() {
        console.log('\n� Getting total count of borrower contacts...');

        try {
            const token = await getAccessToken();
            let totalCount = 0;
            let currentStart = 1;
            const pageSize = 100;
            let hasMore = true;
            let pageCount = 0;

            console.log('🔍 Counting contacts by pagination...');

            while (hasMore && pageCount < 50) { // Limit to 50 pages for counting (50,000 contacts max for counting)
                const response = await axios.post(
                    `${baseUrl}/encompass/v1/borrowerContactSelector`,
                    { start: currentStart, limit: pageSize },
                    {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    }
                );

                let pageContacts = [];
                if (Array.isArray(response.data)) {
                    pageContacts = response.data;
                }

                const contactsInPage = pageContacts.length;
                totalCount += contactsInPage;
                pageCount++;

                console.log(`   📈 Found ${contactsInPage} contacts on page ${pageCount} (Total so far: ${totalCount})`);

                if (contactsInPage < pageSize) {
                    console.log(`   🏁 Last page reached (${contactsInPage} < ${pageSize})`);
                    hasMore = false;
                } else {
                    currentStart += pageSize;
                }

                // Rate limiting
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            if (pageCount >= 50) {
                console.log('⚠️ Stopped counting at 50 pages. Actual total may be higher.');
                console.log(`📊 Estimated minimum total: ${totalCount}+ contacts`);
            } else {
                console.log(`📊 Total borrower contacts found: ${totalCount}`);
            }

            this.totalContacts = totalCount;
            return totalCount;

        } catch (error) {
            console.error('❌ Error getting total count:', error.response?.data || error.message);
            throw error;
        }
    }

    // Get detailed contact information using the contact details endpoint
    async getContactDetails(contactId, token) {
        try {
            const response = await axios.get(
                `${baseUrl}/encompass/v1/borrowerContacts/${contactId}`,
                {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );

            return response.data;
        } catch (error) {
            console.error(`❌ Error fetching details for contact ${contactId}:`, error.response?.data || error.message);
            return null;
        }
    }

    // Fetch exactly 100 contacts and save to JSON
    async fetch100Contacts() {
        console.log('\n📥 Fetching 100 borrower contacts with full details...');

        try {
            // Step 1: Get contact IDs
            const token = await getAccessToken();

            const response = await axios.post(
                `${baseUrl}/encompass/v1/borrowerContactSelector`,
                { start: 1, limit: 100 },
                {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );

            const contactIds = response.data.map(contact => contact.id);
            console.log(`✅ Retrieved ${contactIds.length} contact IDs`);

            // Limit to exactly 100 contacts
            const limitedContactIds = contactIds.slice(0, 100);
            console.log(`🎯 Processing first ${limitedContactIds.length} contacts`);

            // Step 2: Fetch detailed information for each contact
            console.log('\n🔍 Fetching detailed information for each contact...');
            const fullContacts = [];

            for (let i = 0; i < limitedContactIds.length; i++) {
                const contactId = limitedContactIds[i];
                console.log(`📄 Fetching contact ${i + 1}/${limitedContactIds.length}: ${contactId}`);

                const contactDetails = await this.getContactDetails(contactId, token);

                if (contactDetails) {
                    fullContacts.push(contactDetails);

                    // Log key information if available
                    const name = `${contactDetails.firstName || ''} ${contactDetails.lastName || ''}`.trim();
                    const email = contactDetails.personalEmail || contactDetails.workEmail || 'No email';
                    const phone = contactDetails.homePhone || contactDetails.workPhone || contactDetails.mobilePhone || 'No phone';

                    console.log(`   ✅ ${name || 'No name'} | ${email} | ${phone}`);
                } else {
                    console.log(`   ❌ Failed to fetch details for ${contactId}`);
                }

                // Rate limiting to avoid overwhelming the API
                await new Promise(resolve => setTimeout(resolve, 200));
            }

            console.log(`\n✅ Successfully fetched ${fullContacts.length} full contact details`);

            // Save to JSON file
            const filename = `encompass-100-full-contacts-${Date.now()}.json`;
            const filepath = this.saveContactsToFile(fullContacts, filename);

            // Analyze the data
            const analysis = this.analyzeContactData(fullContacts);

            console.log('\n📊 Contact Data Analysis:');
            console.log(`   - Total contacts fetched: ${fullContacts.length}`);
            console.log(`   - Unique fields found: ${analysis.uniqueFields.length}`);
            console.log(`   - Contacts with email: ${analysis.withEmail}`);
            console.log(`   - Contacts with phone: ${analysis.withPhone}`);
            console.log(`   - Contacts with address: ${analysis.withAddress}`);

            // Show sample contact
            if (fullContacts.length > 0) {
                console.log('\n📋 Sample Contact:');
                console.log(JSON.stringify(fullContacts[0], null, 2));
            }

            this.fetchedContacts = fullContacts;
            return {
                contacts: fullContacts,
                filepath,
                analysis
            };

        } catch (error) {
            console.error('❌ Error fetching 100 contacts:', error.response?.data || error.message);
            throw error;
        }
    }

    // Analyze contact data structure
    analyzeContactData(contacts) {
        if (!contacts || contacts.length === 0) {
            return {
                uniqueFields: [],
                withEmail: 0,
                withPhone: 0,
                withAddress: 0,
                fieldPopulation: {}
            };
        }

        const allFields = new Set();
        let withEmail = 0;
        let withPhone = 0;
        let withAddress = 0;

        contacts.forEach(contact => {
            Object.keys(contact).forEach(field => allFields.add(field));

            if (contact.personalEmail || contact.email) withEmail++;
            if (contact.homePhone || contact.phone || contact.mobilePhone) withPhone++;
            if (contact.currentMailingAddress || contact.address) withAddress++;
        });

        // Calculate field population
        const fieldPopulation = {};
        Array.from(allFields).forEach(field => {
            fieldPopulation[field] = contacts.filter(contact =>
                contact[field] !== null &&
                contact[field] !== undefined &&
                contact[field] !== ''
            ).length;
        });

        return {
            uniqueFields: Array.from(allFields),
            withEmail,
            withPhone,
            withAddress,
            fieldPopulation
        };
    }



    // Main execution function
    async runContactFetch() {
        console.log('🚀 Starting Encompass contact fetch for 100 full contacts...\n');

        try {
            // Fetch 100 contacts with full details
            const fetchResult = await this.fetch100Contacts();

            // Summary
            console.log('\n🎉 Process completed successfully!');
            console.log('='.repeat(60));
            console.log(`📥 Contacts fetched and saved: ${fetchResult.contacts.length}`);
            console.log(`💾 Data saved to: ${fetchResult.filepath}`);
            console.log('='.repeat(60));

            // Step 4: Field analysis summary
            console.log('\n📋 Field Analysis Summary:');
            const topFields = Object.entries(fetchResult.analysis.fieldPopulation)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 10);

            topFields.forEach(([field, count]) => {
                const percentage = ((count / fetchResult.contacts.length) * 100).toFixed(1);
                console.log(`   ${field}: ${count}/${fetchResult.contacts.length} (${percentage}%)`);
            });

            return {
                totalCount,
                fetchedContacts: fetchResult.contacts.length,
                filepath: fetchResult.filepath,
                analysis: fetchResult.analysis
            };

        } catch (error) {
            console.error('❌ Process failed:', error.message);
            throw error;
        }
    }


}

// Run contact fetch if called directly
if (require.main === module) {
    const fetcher = new ContactFetcher();
    fetcher.runContactFetch().then(result => {
        console.log('\n✅ Contact fetch completed successfully!');
        console.log('📊 Final Results:', JSON.stringify({
            totalCount: result.totalCount,
            fetchedContacts: result.fetchedContacts,
            savedToFile: result.filepath
        }, null, 2));
    }).catch(error => {
        console.error('❌ Contact fetch failed:', error.message);
        process.exit(1);
    });
}

module.exports = ContactFetcher;
