const tokenManager = require('./encompass-token-manager');
const BorrowerContactsFetcher = require('./fetch-borrower-contacts-paginated');
const axios = require('axios');
require('dotenv').config();

console.log('🧪 Encompass Integration Test Suite');
console.log('='.repeat(50));

// Test configuration
const baseUrl = 'https://api.elliemae.com';
const ghlApiKey = process.env.GOHIGHLEVEL_API_KEY;

class IntegrationTester {
    constructor() {
        this.testResults = [];
    }

    // Add test result
    addResult(testName, success, message, data = null) {
        this.testResults.push({
            test: testName,
            success,
            message,
            data,
            timestamp: new Date().toISOString()
        });

        const status = success ? '✅' : '❌';
        console.log(`${status} ${testName}: ${message}`);
        
        if (data && success) {
            console.log(`   Data: ${JSON.stringify(data, null, 2).substring(0, 200)}...`);
        }
    }

    // Test 1: Token Management
    async testTokenManagement() {
        console.log('\n🔐 Testing Token Management...');
        
        try {
            // Test token info
            const tokenInfo = tokenManager.getTokenInfo();
            this.addResult('Token Info', true, 'Token info retrieved', tokenInfo);

            // Test getting valid token
            const token = await tokenManager.getValidToken();
            this.addResult('Get Valid Token', !!token, token ? 'Token obtained successfully' : 'Failed to get token', {
                tokenPreview: token ? token.substring(0, 20) + '...' : null
            });

            return !!token;
        } catch (error) {
            this.addResult('Token Management', false, error.message);
            return false;
        }
    }

    // Test 2: Encompass API Connection
    async testEncompassConnection() {
        console.log('\n🌐 Testing Encompass API Connection...');
        
        try {
            const token = await tokenManager.getValidToken();
            
            // Test a simple API call - get borrower contacts (first page)
            const response = await axios.post(
                `${baseUrl}/encompass/v1/borrowerContactSelector`,
                {
                    start: "1",
                    limit: "5",
                    cursorType: "randomAccess"
                },
                {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );

            this.addResult('Encompass API Connection', true, `Successfully fetched ${response.data.length} contacts`, {
                contactCount: response.data.length,
                sampleContact: response.data[0] || null
            });

            return true;
        } catch (error) {
            this.addResult('Encompass API Connection', false, error.response?.data?.error || error.message);
            return false;
        }
    }

    // Test 3: Borrower Contacts Fetcher
    async testBorrowerContactsFetcher() {
        console.log('\n📋 Testing Borrower Contacts Fetcher...');
        
        try {
            const fetcher = new BorrowerContactsFetcher();
            
            // Test fetching a single page
            const pageResult = await fetcher.fetchContactsPage(1, 10);
            
            this.addResult('Borrower Contacts Fetcher', true, `Fetched ${pageResult.contacts.length} contacts`, {
                contactCount: pageResult.contacts.length,
                hasMore: pageResult.hasMore,
                nextStart: pageResult.nextStart
            });

            // Test fetching contact details if we have contacts
            if (pageResult.contacts.length > 0 && pageResult.contacts[0].id) {
                const contactDetails = await fetcher.fetchContactDetails(pageResult.contacts[0].id);
                this.addResult('Contact Details Fetch', !!contactDetails, contactDetails ? 'Contact details retrieved' : 'Failed to get contact details', {
                    contactId: pageResult.contacts[0].id,
                    hasDetails: !!contactDetails
                });
            }

            return true;
        } catch (error) {
            this.addResult('Borrower Contacts Fetcher', false, error.message);
            return false;
        }
    }

    // Test 4: GoHighLevel API Connection
    async testGoHighLevelConnection() {
        console.log('\n🎯 Testing GoHighLevel API Connection...');
        
        if (!ghlApiKey) {
            this.addResult('GoHighLevel API Connection', false, 'GHL API key not configured');
            return false;
        }

        try {
            // Test GHL API with a simple call (get contacts)
            const response = await axios.get(
                'https://services.leadconnectorhq.com/contacts/',
                {
                    headers: {
                        'Authorization': `Bearer ${ghlApiKey}`,
                        'Content-Type': 'application/json',
                        'Version': '2021-07-28'
                    },
                    params: {
                        limit: 1
                    }
                }
            );

            this.addResult('GoHighLevel API Connection', true, 'Successfully connected to GHL API', {
                contactCount: response.data.contacts?.length || 0
            });

            return true;
        } catch (error) {
            this.addResult('GoHighLevel API Connection', false, error.response?.data?.message || error.message);
            return false;
        }
    }

    // Test 5: Data Transformation
    async testDataTransformation() {
        console.log('\n🔄 Testing Data Transformation...');
        
        try {
            // Create sample Encompass borrower data
            const sampleBorrowerData = {
                id: 'test-123',
                firstName: 'John',
                lastName: 'Doe',
                personalEmail: '<EMAIL>',
                homePhone: '************',
                currentMailingAddress: {
                    addressLineText: '123 Main St',
                    cityName: 'Anytown',
                    stateCode: 'CA',
                    postalCode: '12345'
                },
                ownerId: 'owner-123',
                birthdate: '1980-01-01',
                referral: 'Website'
            };

            // Transform to GHL format
            const ghlContactData = {
                firstName: sampleBorrowerData.firstName || '',
                lastName: sampleBorrowerData.lastName || '',
                email: sampleBorrowerData.personalEmail || '',
                phone: sampleBorrowerData.homePhone || '',
                address1: sampleBorrowerData.currentMailingAddress?.addressLineText || '',
                city: sampleBorrowerData.currentMailingAddress?.cityName || '',
                state: sampleBorrowerData.currentMailingAddress?.stateCode || '',
                postalCode: sampleBorrowerData.currentMailingAddress?.postalCode || '',
                source: 'Encompass Integration',
                tags: ['Encompass Lead', 'Borrower Contact'],
                customFields: {
                    encompass_id: sampleBorrowerData.id,
                    encompass_owner_id: sampleBorrowerData.ownerId,
                    birth_date: sampleBorrowerData.birthdate,
                    referral_source: sampleBorrowerData.referral
                }
            };

            this.addResult('Data Transformation', true, 'Successfully transformed Encompass data to GHL format', {
                originalFields: Object.keys(sampleBorrowerData).length,
                transformedFields: Object.keys(ghlContactData).length,
                customFields: Object.keys(ghlContactData.customFields).length
            });

            return true;
        } catch (error) {
            this.addResult('Data Transformation', false, error.message);
            return false;
        }
    }

    // Run all tests
    async runAllTests() {
        console.log('🚀 Starting comprehensive integration tests...\n');

        const tests = [
            () => this.testTokenManagement(),
            () => this.testEncompassConnection(),
            () => this.testBorrowerContactsFetcher(),
            () => this.testGoHighLevelConnection(),
            () => this.testDataTransformation()
        ];

        for (const test of tests) {
            try {
                await test();
            } catch (error) {
                console.error('❌ Test failed with error:', error.message);
            }
            
            // Small delay between tests
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        this.printSummary();
    }

    // Print test summary
    printSummary() {
        console.log('\n📊 Test Summary');
        console.log('='.repeat(50));

        const passed = this.testResults.filter(r => r.success).length;
        const failed = this.testResults.filter(r => !r.success).length;
        const total = this.testResults.length;

        console.log(`✅ Passed: ${passed}/${total}`);
        console.log(`❌ Failed: ${failed}/${total}`);
        console.log(`📈 Success Rate: ${((passed / total) * 100).toFixed(1)}%`);

        if (failed > 0) {
            console.log('\n❌ Failed Tests:');
            this.testResults.filter(r => !r.success).forEach(result => {
                console.log(`   - ${result.test}: ${result.message}`);
            });
        }

        console.log('\n🎯 Next Steps:');
        if (passed === total) {
            console.log('✅ All tests passed! You can now:');
            console.log('   1. Set up webhooks: node setup-encompass-webhooks.js');
            console.log('   2. Start webhook server: node encompass-webhook-integration.js');
            console.log('   3. Test with real data: node fetch-borrower-contacts-paginated.js --max-pages 1');
        } else {
            console.log('⚠️ Some tests failed. Please fix the issues before proceeding:');
            console.log('   1. Check your .env file configuration');
            console.log('   2. Verify API credentials');
            console.log('   3. Ensure network connectivity');
        }

        console.log('='.repeat(50));
    }
}

// Run tests if called directly
if (require.main === module) {
    const tester = new IntegrationTester();
    tester.runAllTests().catch(error => {
        console.error('❌ Test suite failed:', error.message);
        process.exit(1);
    });
}

module.exports = IntegrationTester;
