const axios = require('axios');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

console.log('🧪 Encompass Contact Fetcher - 1000 Contacts + Total Count');
console.log('='.repeat(60));

// Configuration
const baseUrl = 'https://api.elliemae.com';
const username = process.env.ENCOMPASS_USERNAME;
const password = process.env.ENCOMPASS_PASSWORD;
const clientId = process.env.ENCOMPASS_CLIENT_ID;
const clientSecret = process.env.ENCOMPASS_CLIENT_SECRET;

// Ensure data directory exists
const dataDir = path.join(__dirname, 'data');
if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
}

// Authentication function (borrowed from working files)
async function getAccessToken() {
    try {
        console.log('🔐 Getting access token...');

        const tokenResponse = await axios.post(`${baseUrl}/oauth2/v1/token`,
            `grant_type=password&username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`,
            {
                headers: {
                    'Authorization': `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`,
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            }
        );

        console.log('✅ Access token obtained successfully');
        return tokenResponse.data.access_token;
    } catch (error) {
        console.error('❌ Error getting access token:', error.response?.data || error.message);
        throw error;
    }
}

class ContactFetcher {
    constructor() {
        this.totalContacts = 0;
        this.fetchedContacts = [];
        this.startTime = Date.now();
    }

    // Save contacts to JSON file
    saveContactsToFile(contacts, filename) {
        try {
            const timestamp = Date.now();
            const fullFilename = filename || `encompass-contacts-${timestamp}.json`;
            const filepath = path.join(dataDir, fullFilename);

            const dataToSave = {
                metadata: {
                    totalContacts: contacts.length,
                    fetchedAt: new Date().toISOString(),
                    elapsedTime: (Date.now() - this.startTime) / 1000,
                    source: 'Encompass API'
                },
                contacts: contacts
            };

            fs.writeFileSync(filepath, JSON.stringify(dataToSave, null, 2));
            console.log(`💾 Contacts saved to: ${fullFilename}`);
            console.log(`📁 Full path: ${filepath}`);
            return filepath;
        } catch (error) {
            console.error('❌ Error saving contacts:', error.message);
            return null;
        }
    }

    // Get total count of borrower contacts
    async getTotalContactCount() {
        console.log('\n� Getting total count of borrower contacts...');

        try {
            const token = await getAccessToken();
            let totalCount = 0;
            let currentStart = 1;
            const pageSize = 1000;
            let hasMore = true;
            let pageCount = 0;

            console.log('🔍 Counting contacts by pagination...');

            while (hasMore && pageCount < 50) { // Limit to 50 pages for counting (50,000 contacts max for counting)
                const response = await axios.post(
                    `${baseUrl}/encompass/v1/borrowerContactSelector`,
                    { start: currentStart, limit: pageSize },
                    {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    }
                );

                let pageContacts = [];
                if (Array.isArray(response.data)) {
                    pageContacts = response.data;
                }

                const contactsInPage = pageContacts.length;
                totalCount += contactsInPage;
                pageCount++;

                console.log(`   📈 Found ${contactsInPage} contacts on page ${pageCount} (Total so far: ${totalCount})`);

                if (contactsInPage < pageSize) {
                    console.log(`   🏁 Last page reached (${contactsInPage} < ${pageSize})`);
                    hasMore = false;
                } else {
                    currentStart += pageSize;
                }

                // Rate limiting
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            if (pageCount >= 50) {
                console.log('⚠️ Stopped counting at 50 pages. Actual total may be higher.');
                console.log(`📊 Estimated minimum total: ${totalCount}+ contacts`);
            } else {
                console.log(`📊 Total borrower contacts found: ${totalCount}`);
            }

            this.totalContacts = totalCount;
            return totalCount;

        } catch (error) {
            console.error('❌ Error getting total count:', error.response?.data || error.message);
            throw error;
        }
    }

    // Fetch exactly 1000 contacts and save to JSON
    async fetch1000Contacts() {
        console.log('\n📥 Fetching 1000 borrower contacts...');

        try {
            const token = await getAccessToken();

            const response = await axios.post(
                `${baseUrl}/encompass/v1/borrowerContactSelector`,
                { start: 1, limit: 1000 },
                {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );

            const contacts = response.data;
            console.log(`✅ Successfully fetched ${contacts.length} contacts`);

            // Save to JSON file
            const filename = `encompass-1000-contacts-${Date.now()}.json`;
            const filepath = this.saveContactsToFile(contacts, filename);

            // Analyze the data
            const analysis = this.analyzeContactData(contacts);

            console.log('\n📊 Contact Data Analysis:');
            console.log(`   - Total contacts fetched: ${contacts.length}`);
            console.log(`   - Unique fields found: ${analysis.uniqueFields.length}`);
            console.log(`   - Contacts with email: ${analysis.withEmail}`);
            console.log(`   - Contacts with phone: ${analysis.withPhone}`);
            console.log(`   - Contacts with address: ${analysis.withAddress}`);

            // Show sample contact
            if (contacts.length > 0) {
                console.log('\n📋 Sample Contact:');
                console.log(JSON.stringify(contacts[0], null, 2));
            }

            this.fetchedContacts = contacts;
            return {
                contacts,
                filepath,
                analysis
            };

        } catch (error) {
            console.error('❌ Error fetching 1000 contacts:', error.response?.data || error.message);
            throw error;
        }
    }

    // Analyze contact data structure
    analyzeContactData(contacts) {
        if (!contacts || contacts.length === 0) {
            return {
                uniqueFields: [],
                withEmail: 0,
                withPhone: 0,
                withAddress: 0,
                fieldPopulation: {}
            };
        }

        const allFields = new Set();
        let withEmail = 0;
        let withPhone = 0;
        let withAddress = 0;

        contacts.forEach(contact => {
            Object.keys(contact).forEach(field => allFields.add(field));

            if (contact.personalEmail || contact.email) withEmail++;
            if (contact.homePhone || contact.phone || contact.mobilePhone) withPhone++;
            if (contact.currentMailingAddress || contact.address) withAddress++;
        });

        // Calculate field population
        const fieldPopulation = {};
        Array.from(allFields).forEach(field => {
            fieldPopulation[field] = contacts.filter(contact =>
                contact[field] !== null &&
                contact[field] !== undefined &&
                contact[field] !== ''
            ).length;
        });

        return {
            uniqueFields: Array.from(allFields),
            withEmail,
            withPhone,
            withAddress,
            fieldPopulation
        };
    }



    // Main execution function
    async runContactFetch() {
        console.log('🚀 Starting Encompass contact fetch and count...\n');

        try {
            // Step 1: Get total count
            console.log('📊 Step 1: Getting total borrower contact count...');
            const totalCount = await this.getTotalContactCount();

            // Step 2: Fetch 1000 contacts
            console.log('\n📥 Step 2: Fetching 1000 contacts...');
            const fetchResult = await this.fetch1000Contacts();

            // Step 3: Summary
            console.log('\n🎉 Process completed successfully!');
            console.log('='.repeat(60));
            console.log(`📊 Total borrower contacts in database: ${totalCount}`);
            console.log(`📥 Contacts fetched and saved: ${fetchResult.contacts.length}`);
            console.log(`💾 Data saved to: ${fetchResult.filepath}`);
            console.log('='.repeat(60));

            // Step 4: Field analysis summary
            console.log('\n📋 Field Analysis Summary:');
            const topFields = Object.entries(fetchResult.analysis.fieldPopulation)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 10);

            topFields.forEach(([field, count]) => {
                const percentage = ((count / fetchResult.contacts.length) * 100).toFixed(1);
                console.log(`   ${field}: ${count}/${fetchResult.contacts.length} (${percentage}%)`);
            });

            return {
                totalCount,
                fetchedContacts: fetchResult.contacts.length,
                filepath: fetchResult.filepath,
                analysis: fetchResult.analysis
            };

        } catch (error) {
            console.error('❌ Process failed:', error.message);
            throw error;
        }
    }


}

// Run contact fetch if called directly
if (require.main === module) {
    const fetcher = new ContactFetcher();
    fetcher.runContactFetch().then(result => {
        console.log('\n✅ Contact fetch completed successfully!');
        console.log('📊 Final Results:', JSON.stringify({
            totalCount: result.totalCount,
            fetchedContacts: result.fetchedContacts,
            savedToFile: result.filepath
        }, null, 2));
    }).catch(error => {
        console.error('❌ Contact fetch failed:', error.message);
        process.exit(1);
    });
}

module.exports = ContactFetcher;
