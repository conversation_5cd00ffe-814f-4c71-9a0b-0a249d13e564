const express = require('express');
const crypto = require('crypto');
const BorrowerService = require('./borrower-service');
const GoHighLevelClient = require('../utils/gohighlevel-client');

/**
 * Webhook Service for Encompass
 * Handles real-time notifications when borrowers are created/updated
 */
class WebhookService {
    constructor(config = {}) {
        this.app = express();
        this.port = config.port || process.env.WEBHOOK_PORT || 3000;
        this.signingKey = config.signingKey || process.env.ENCOMPASS_WEBHOOK_SIGNING_KEY;
        
        this.borrowerService = new BorrowerService(config);
        this.ghlClient = new GoHighLevelClient(config);
        
        this.setupMiddleware();
        this.setupRoutes();
        
        // Statistics
        this.stats = {
            totalWebhooks: 0,
            borrowerEvents: 0,
            successfulProcessing: 0,
            errors: 0,
            startTime: Date.now()
        };
    }

    /**
     * Setup Express middleware
     */
    setupMiddleware() {
        // Raw body parser for webhook signature verification
        this.app.use('/webhook', express.raw({ type: 'application/json' }));
        
        // JSON parser for other routes
        this.app.use(express.json());
        
        // CORS headers
        this.app.use((req, res, next) => {
            res.header('Access-Control-Allow-Origin', '*');
            res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
            res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
            next();
        });

        // Request logging
        this.app.use((req, res, next) => {
            console.log(`[${new Date().toISOString()}] ${req.method} ${req.path} - ${req.ip}`);
            next();
        });
    }

    /**
     * Setup Express routes
     */
    setupRoutes() {
        // Health check endpoint
        this.app.get('/health', (req, res) => {
            res.json({
                status: 'healthy',
                timestamp: new Date().toISOString(),
                uptime: Date.now() - this.stats.startTime,
                stats: this.stats
            });
        });

        // Webhook endpoint for Encompass notifications
        this.app.post('/webhook/encompass', this.handleEncompassWebhook.bind(this));

        // Statistics endpoint
        this.app.get('/stats', (req, res) => {
            res.json({
                ...this.stats,
                uptime: Date.now() - this.stats.startTime,
                uptimeFormatted: this.formatUptime(Date.now() - this.stats.startTime)
            });
        });

        // Test endpoint
        this.app.post('/test', (req, res) => {
            res.json({
                message: 'Test endpoint working',
                timestamp: new Date().toISOString(),
                body: req.body
            });
        });
    }

    /**
     * Verify webhook signature from Encompass
     * @param {Buffer} payload - Raw request body
     * @param {string} signature - Signature from header
     * @returns {boolean} True if signature is valid
     */
    verifySignature(payload, signature) {
        if (!this.signingKey || !signature) {
            console.warn(`[${new Date().toISOString()}] ⚠️ Webhook signature verification skipped (missing key or signature)`);
            return true; // Allow for development/testing
        }

        try {
            const expectedSignature = crypto
                .createHmac('sha256', this.signingKey)
                .update(payload)
                .digest('hex');

            const providedSignature = signature.replace('sha256=', '');
            
            return crypto.timingSafeEqual(
                Buffer.from(expectedSignature, 'hex'),
                Buffer.from(providedSignature, 'hex')
            );
        } catch (error) {
            console.error(`[${new Date().toISOString()}] ❌ Error verifying webhook signature:`, error.message);
            return false;
        }
    }

    /**
     * Handle Encompass webhook notifications
     * @param {object} req - Express request object
     * @param {object} res - Express response object
     */
    async handleEncompassWebhook(req, res) {
        this.stats.totalWebhooks++;
        
        try {
            // Verify signature
            const signature = req.headers['x-encompass-signature'] || req.headers['x-hub-signature-256'];
            if (!this.verifySignature(req.body, signature)) {
                console.error(`[${new Date().toISOString()}] ❌ Invalid webhook signature`);
                return res.status(401).json({ error: 'Invalid signature' });
            }

            // Parse webhook payload
            const payload = JSON.parse(req.body.toString());
            console.log(`[${new Date().toISOString()}] 📨 Received webhook:`, {
                eventType: payload.eventType,
                resourceType: payload.meta?.resourceType,
                resourceId: payload.meta?.resourceId,
                eventId: payload.eventId
            });

            // Check if this is a borrower-related event
            if (this.isBorrowerEvent(payload)) {
                this.stats.borrowerEvents++;
                await this.processBorrowerEvent(payload);
                this.stats.successfulProcessing++;
            } else {
                console.log(`[${new Date().toISOString()}] ℹ️ Ignoring non-borrower event: ${payload.meta?.resourceType}`);
            }

            // Respond to Encompass
            res.status(200).json({
                status: 'received',
                eventId: payload.eventId,
                timestamp: new Date().toISOString()
            });

        } catch (error) {
            this.stats.errors++;
            console.error(`[${new Date().toISOString()}] ❌ Error processing webhook:`, error.message);
            res.status(500).json({
                error: 'Internal server error',
                timestamp: new Date().toISOString()
            });
        }
    }

    /**
     * Check if webhook event is related to borrowers
     * @param {object} payload - Webhook payload
     * @returns {boolean} True if borrower-related event
     */
    isBorrowerEvent(payload) {
        const resourceType = payload.meta?.resourceType;
        const eventType = payload.eventType;
        
        // Check for borrower contact events
        if (resourceType === 'BorrowerContacts' || resourceType === 'borrowerContacts') {
            return ['create', 'update'].includes(eventType);
        }

        // Check for internal user events that might indicate new borrowers
        if (resourceType === 'InternalUsers' && eventType === 'create') {
            return true;
        }

        return false;
    }

    /**
     * Process borrower-related webhook events
     * @param {object} payload - Webhook payload
     */
    async processBorrowerEvent(payload) {
        try {
            console.log(`[${new Date().toISOString()}] 🔄 Processing borrower event: ${payload.eventType}`);

            // Extract borrower ID from payload
            const borrowerId = this.extractBorrowerId(payload);
            if (!borrowerId) {
                console.warn(`[${new Date().toISOString()}] ⚠️ Could not extract borrower ID from webhook payload`);
                return;
            }

            // Fetch full borrower details from Encompass
            console.log(`[${new Date().toISOString()}] 📥 Fetching borrower details for ID: ${borrowerId}`);
            const borrowerDetails = await this.borrowerService.getBorrowerContactDetails(borrowerId);

            if (!borrowerDetails) {
                console.warn(`[${new Date().toISOString()}] ⚠️ Could not fetch borrower details for ID: ${borrowerId}`);
                return;
            }

            console.log(`[${new Date().toISOString()}] ✅ Retrieved borrower: ${borrowerDetails.firstName} ${borrowerDetails.lastName}`);

            // Transform to GoHighLevel format
            const ghlContactData = this.borrowerService.transformToGHLFormat(borrowerDetails);

            // Create or update contact in GoHighLevel
            console.log(`[${new Date().toISOString()}] 🎯 Pushing to GoHighLevel...`);
            const ghlResult = await this.ghlClient.createOrUpdateContact(ghlContactData);

            if (ghlResult.success) {
                console.log(`[${new Date().toISOString()}] ✅ GoHighLevel: Contact ${ghlResult.action} successfully`);
                console.log(`[${new Date().toISOString()}] 🆔 GHL Contact ID: ${ghlResult.contactId}`);

                // Create opportunity if configured
                if (ghlResult.contactId && this.ghlClient.pipelineId && this.ghlClient.pipelineStageId) {
                    console.log(`[${new Date().toISOString()}] 🎯 Creating opportunity...`);
                    const opportunityResult = await this.ghlClient.createOpportunity(ghlResult.contactId, ghlContactData);
                    
                    if (opportunityResult.success) {
                        console.log(`[${new Date().toISOString()}] ✅ Opportunity ${opportunityResult.isNew ? 'created' : 'found'}: ${opportunityResult.opportunityId}`);
                    }
                }
            }

        } catch (error) {
            console.error(`[${new Date().toISOString()}] ❌ Error processing borrower event:`, error.message);
            throw error;
        }
    }

    /**
     * Extract borrower ID from webhook payload
     * @param {object} payload - Webhook payload
     * @returns {string|null} Borrower ID
     */
    extractBorrowerId(payload) {
        // Try different possible locations for the borrower ID
        return payload.meta?.resourceId ||
               payload.meta?.payload?.entities?.[0]?.id ||
               payload.entityId ||
               payload.payload?.entities?.[0]?.id ||
               null;
    }

    /**
     * Format uptime in human-readable format
     * @param {number} ms - Milliseconds
     * @returns {string} Formatted uptime
     */
    formatUptime(ms) {
        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (days > 0) return `${days}d ${hours % 24}h ${minutes % 60}m`;
        if (hours > 0) return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
        if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
        return `${seconds}s`;
    }

    /**
     * Start the webhook server
     */
    start() {
        this.app.listen(this.port, () => {
            console.log(`[${new Date().toISOString()}] 🚀 Webhook server started on port ${this.port}`);
            console.log(`[${new Date().toISOString()}] 📡 Webhook endpoint: http://localhost:${this.port}/webhook/encompass`);
            console.log(`[${new Date().toISOString()}] 🏥 Health check: http://localhost:${this.port}/health`);
            console.log(`[${new Date().toISOString()}] 📊 Statistics: http://localhost:${this.port}/stats`);
        });
    }

    /**
     * Stop the webhook server
     */
    stop() {
        if (this.server) {
            this.server.close();
            console.log(`[${new Date().toISOString()}] 🛑 Webhook server stopped`);
        }
    }
}

module.exports = WebhookService;
