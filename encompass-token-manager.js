const axios = require('axios');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Configuration
const baseUrl = 'https://api.elliemae.com';
const username = process.env.ENCOMPASS_USERNAME;
const password = process.env.ENCOMPASS_PASSWORD;
const clientId = process.env.ENCOMPASS_CLIENT_ID;
const clientSecret = process.env.ENCOMPASS_CLIENT_SECRET;

// Token storage file
const TOKEN_FILE = path.join(__dirname, 'data', 'encompass-token.json');

// Ensure data directory exists
const dataDir = path.join(__dirname, 'data');
if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
}

class EncompassTokenManager {
    constructor() {
        this.token = null;
        this.expiresAt = null;
        this.loadTokenFromFile();
    }

    // Load token from file if it exists and is valid
    loadTokenFromFile() {
        try {
            if (fs.existsSync(TOKEN_FILE)) {
                const tokenData = JSON.parse(fs.readFileSync(TOKEN_FILE, 'utf8'));
                
                if (tokenData.expiresAt && new Date(tokenData.expiresAt) > new Date()) {
                    this.token = tokenData.access_token;
                    this.expiresAt = new Date(tokenData.expiresAt);
                    console.log('✅ Loaded valid token from file, expires at:', this.expiresAt.toISOString());
                    return true;
                } else {
                    console.log('⚠️ Token in file has expired, will fetch new one');
                }
            }
        } catch (error) {
            console.log('⚠️ Error loading token from file:', error.message);
        }
        return false;
    }

    // Save token to file
    saveTokenToFile(tokenData) {
        try {
            const dataToSave = {
                access_token: tokenData.access_token,
                token_type: tokenData.token_type,
                expires_in: tokenData.expires_in,
                expiresAt: this.expiresAt.toISOString(),
                fetchedAt: new Date().toISOString()
            };

            fs.writeFileSync(TOKEN_FILE, JSON.stringify(dataToSave, null, 2));
            console.log('💾 Token saved to file:', TOKEN_FILE);
        } catch (error) {
            console.error('❌ Error saving token to file:', error.message);
        }
    }

    // Check if current token is valid (not expired)
    isTokenValid() {
        return this.token && this.expiresAt && new Date() < this.expiresAt;
    }

    // Get a fresh token from Encompass API
    async fetchNewToken() {
        try {
            console.log('🔐 Fetching new token from Encompass API...');
            
            const response = await axios.post(`${baseUrl}/oauth2/v1/token`, {
                grant_type: 'password',
                username: username,
                password: password,
                client_id: clientId,
                client_secret: clientSecret
            }, {
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const tokenData = response.data;
            this.token = tokenData.access_token;
            
            // Calculate expiration time (subtract 5 minutes for safety)
            const expiresInMs = (tokenData.expires_in - 300) * 1000;
            this.expiresAt = new Date(Date.now() + expiresInMs);

            console.log('✅ New token fetched successfully');
            console.log('⏰ Token expires at:', this.expiresAt.toISOString());

            // Save to file
            this.saveTokenToFile(tokenData);

            return this.token;
        } catch (error) {
            console.error('❌ Error fetching new token:', error.response?.data || error.message);
            throw error;
        }
    }

    // Get a valid token (fetch new one if current is expired)
    async getValidToken() {
        if (this.isTokenValid()) {
            console.log('✅ Using existing valid token');
            return this.token;
        }

        console.log('🔄 Token expired or missing, fetching new one...');
        return await this.fetchNewToken();
    }

    // Force refresh token
    async refreshToken() {
        console.log('🔄 Force refreshing token...');
        return await this.fetchNewToken();
    }

    // Get token info
    getTokenInfo() {
        return {
            hasToken: !!this.token,
            isValid: this.isTokenValid(),
            expiresAt: this.expiresAt?.toISOString(),
            timeUntilExpiry: this.expiresAt ? Math.max(0, this.expiresAt.getTime() - Date.now()) : 0
        };
    }
}

// Export singleton instance
const tokenManager = new EncompassTokenManager();

module.exports = tokenManager;

// CLI usage
if (require.main === module) {
    const args = process.argv.slice(2);
    const command = args[0];

    async function runCommand() {
        try {
            switch (command) {
                case 'info':
                    console.log('📋 Token Information:');
                    console.log(JSON.stringify(tokenManager.getTokenInfo(), null, 2));
                    break;

                case 'refresh':
                    await tokenManager.refreshToken();
                    console.log('🎉 Token refreshed successfully');
                    break;

                case 'get':
                    const token = await tokenManager.getValidToken();
                    console.log('🔑 Valid token obtained');
                    console.log('Token preview:', token.substring(0, 20) + '...');
                    break;

                default:
                    console.log('📖 Usage:');
                    console.log('  node encompass-token-manager.js info     - Show token information');
                    console.log('  node encompass-token-manager.js refresh  - Force refresh token');
                    console.log('  node encompass-token-manager.js get      - Get valid token');
                    break;
            }
        } catch (error) {
            console.error('❌ Command failed:', error.message);
            process.exit(1);
        }
    }

    runCommand();
}
