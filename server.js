#!/usr/bin/env node

/**
 * Comprehensive Encompass to GoHighLevel Integration Server
 * Consolidates all functionality into a single Express server
 */

require('dotenv').config();
const express = require('express');
const path = require('path');
const fs = require('fs');

// Import services
const BorrowerService = require('./src/services/borrower-service');
const GoHighLevelClient = require('./src/utils/gohighlevel-client');
const WebhookService = require('./src/services/webhook-service');
const IntegrationService = require('./src/integration-service');

class ComprehensiveServer {
    constructor() {
        this.app = express();
        this.port = process.env.PORT || process.env.WEBHOOK_PORT || 3000;

        // Configuration object
        const config = {
            baseUrl: process.env.ENCOMPASS_API_URL,
            clientId: process.env.ENCOMPASS_CLIENT_ID,
            clientSecret: process.env.ENCOMPASS_CLIENT_SECRET,
            username: process.env.ENCOMPASS_USERNAME,
            password: process.env.ENCOMPASS_PASSWORD,
            apiKey: process.env.GOHIGHLEVEL_API_KEY,
            locationId: process.env.GOHIGHLEVEL_LOCATION_ID,
            pipelineId: process.env.GOHIGHLEVEL_PIPELINE_ID,
            pipelineStageId: process.env.GOHIGHLEVEL_PIPELINE_STAGE_ID
        };

        // Initialize services
        this.borrowerService = new BorrowerService(config);
        this.ghlClient = new GoHighLevelClient(config);
        this.integrationService = new IntegrationService(config);
        
        // Statistics
        this.stats = {
            serverStartTime: Date.now(),
            totalRequests: 0,
            integrationRuns: 0,
            webhookEvents: 0,
            errors: 0
        };

        this.setupMiddleware();
        this.setupRoutes();
    }

    setupMiddleware() {
        // Raw body parser for webhook signature verification
        this.app.use('/webhook', express.raw({ type: 'application/json' }));
        
        // JSON parser for other routes
        this.app.use(express.json());
        
        // Static files
        this.app.use('/static', express.static(path.join(__dirname, 'public')));
        
        // CORS headers
        this.app.use((req, res, next) => {
            res.header('Access-Control-Allow-Origin', '*');
            res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
            res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
            next();
        });

        // Request logging and statistics
        this.app.use((req, res, next) => {
            this.stats.totalRequests++;
            console.log(`[${new Date().toISOString()}] ${req.method} ${req.path} - ${req.ip}`);
            next();
        });
    }

    setupRoutes() {
        // Health check endpoint
        this.app.get('/health', (req, res) => {
            res.json({
                status: 'healthy',
                timestamp: new Date().toISOString(),
                uptime: Date.now() - this.stats.serverStartTime,
                stats: this.stats,
                services: {
                    encompass: 'connected',
                    gohighlevel: 'connected'
                }
            });
        });

        // Dashboard endpoint
        this.app.get('/', this.renderDashboard.bind(this));

        // Borrower endpoints
        this.app.get('/api/borrowers/count', this.getBorrowerCount.bind(this));
        this.app.get('/api/borrowers', this.getAllBorrowers.bind(this));
        this.app.get('/api/borrowers/sample/:limit?', this.getSampleBorrowers.bind(this));
        this.app.post('/api/borrowers/test-extraction', this.testDataExtraction.bind(this));

        // Integration endpoints
        this.app.post('/api/integration/run', this.runIntegration.bind(this));
        this.app.get('/api/integration/status', this.getIntegrationStatus.bind(this));
        this.app.get('/api/integration/results', this.getIntegrationResults.bind(this));

        // Webhook endpoints
        this.app.post('/webhook/encompass', this.handleWebhook.bind(this));
        this.app.post('/api/webhook/test', this.testWebhook.bind(this));

        // Statistics endpoint
        this.app.get('/api/stats', (req, res) => {
            res.json({
                ...this.stats,
                uptime: Date.now() - this.stats.serverStartTime,
                uptimeFormatted: this.formatUptime(Date.now() - this.stats.serverStartTime)
            });
        });

        // Configuration endpoints
        this.app.get('/api/config', this.getConfiguration.bind(this));
        this.app.post('/api/config/test', this.testConfiguration.bind(this));

        // File management endpoints
        this.app.get('/api/files/results', this.listResultFiles.bind(this));
        this.app.get('/api/files/results/:filename', this.getResultFile.bind(this));
        this.app.delete('/api/files/cleanup', this.cleanupFiles.bind(this));
    }

    // Dashboard rendering
    renderDashboard(req, res) {
        const html = `
        <!DOCTYPE html>
        <html>
        <head>
            <title>Encompass to GoHighLevel Integration</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
                .container { max-width: 1200px; margin: 0 auto; }
                .card { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                .header { text-align: center; color: #333; }
                .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; }
                .stat { text-align: center; padding: 15px; background: #e3f2fd; border-radius: 8px; }
                .stat h3 { margin: 0; color: #1976d2; }
                .stat p { margin: 5px 0 0 0; font-size: 24px; font-weight: bold; }
                .actions { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; }
                .action { padding: 15px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; }
                .action h4 { margin: 0 0 10px 0; color: #495057; }
                .btn { padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; }
                .btn:hover { background: #0056b3; }
                .btn-success { background: #28a745; }
                .btn-warning { background: #ffc107; color: #212529; }
                .btn-danger { background: #dc3545; }
                .log { background: #f8f9fa; padding: 15px; border-radius: 8px; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="card">
                    <div class="header">
                        <h1>🚀 Encompass to GoHighLevel Integration</h1>
                        <p>Comprehensive integration dashboard and control panel</p>
                    </div>
                </div>

                <div class="card">
                    <h2>📊 System Statistics</h2>
                    <div class="stats" id="stats">
                        <div class="stat">
                            <h3>Server Uptime</h3>
                            <p id="uptime">Loading...</p>
                        </div>
                        <div class="stat">
                            <h3>Total Requests</h3>
                            <p id="requests">${this.stats.totalRequests}</p>
                        </div>
                        <div class="stat">
                            <h3>Integration Runs</h3>
                            <p id="integrations">${this.stats.integrationRuns}</p>
                        </div>
                        <div class="stat">
                            <h3>Webhook Events</h3>
                            <p id="webhooks">${this.stats.webhookEvents}</p>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <h2>🛠️ Actions</h2>
                    <div class="actions">
                        <div class="action">
                            <h4>📊 Borrower Statistics</h4>
                            <p>Get total borrower count and sample data</p>
                            <button class="btn" onclick="getBorrowerCount()">Get Count</button>
                            <button class="btn btn-success" onclick="getSampleBorrowers()">Sample Data</button>
                        </div>
                        <div class="action">
                            <h4>🔄 Run Integration</h4>
                            <p>Process borrowers and sync to GoHighLevel</p>
                            <input type="number" id="contactLimit" value="10" min="1" max="100" style="width: 60px;">
                            <button class="btn btn-success" onclick="runIntegration()">Run Integration</button>
                        </div>
                        <div class="action">
                            <h4>🧪 Test Webhook</h4>
                            <p>Test webhook endpoint functionality</p>
                            <button class="btn btn-warning" onclick="testWebhook()">Test Webhook</button>
                        </div>
                        <div class="action">
                            <h4>📁 File Management</h4>
                            <p>View and manage integration results</p>
                            <button class="btn" onclick="listFiles()">List Files</button>
                            <button class="btn btn-danger" onclick="cleanupFiles()">Cleanup</button>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <h2>📋 Activity Log</h2>
                    <div class="log" id="log">
                        <p>Activity log will appear here...</p>
                    </div>
                </div>
            </div>

            <script>
                // Auto-refresh stats every 5 seconds
                setInterval(updateStats, 5000);
                updateStats();

                function updateStats() {
                    fetch('/api/stats')
                        .then(r => r.json())
                        .then(data => {
                            document.getElementById('uptime').textContent = data.uptimeFormatted;
                            document.getElementById('requests').textContent = data.totalRequests;
                            document.getElementById('integrations').textContent = data.integrationRuns;
                            document.getElementById('webhooks').textContent = data.webhookEvents;
                        });
                }

                function log(message) {
                    const logDiv = document.getElementById('log');
                    const timestamp = new Date().toLocaleTimeString();
                    logDiv.innerHTML += '<br>' + timestamp + ' - ' + message;
                    logDiv.scrollTop = logDiv.scrollHeight;
                }

                function getBorrowerCount() {
                    log('Getting borrower count...');
                    fetch('/api/borrowers/count')
                        .then(r => r.json())
                        .then(data => log('Total borrowers: ' + data.count))
                        .catch(e => log('Error: ' + e.message));
                }

                function getSampleBorrowers() {
                    log('Getting sample borrowers...');
                    fetch('/api/borrowers/sample/5')
                        .then(r => r.json())
                        .then(data => log('Retrieved ' + data.borrowers.length + ' sample borrowers'))
                        .catch(e => log('Error: ' + e.message));
                }

                function runIntegration() {
                    const limit = document.getElementById('contactLimit').value;
                    log('Starting integration for ' + limit + ' contacts...');
                    fetch('/api/integration/run', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify({limit: parseInt(limit)})
                    })
                    .then(r => r.json())
                    .then(data => log('Integration completed: ' + data.processed + ' processed'))
                    .catch(e => log('Error: ' + e.message));
                }

                function testWebhook() {
                    log('Testing webhook...');
                    fetch('/api/webhook/test', {method: 'POST'})
                        .then(r => r.json())
                        .then(data => log('Webhook test: ' + data.status))
                        .catch(e => log('Error: ' + e.message));
                }

                function listFiles() {
                    log('Listing result files...');
                    fetch('/api/files/results')
                        .then(r => r.json())
                        .then(data => log('Found ' + data.files.length + ' result files'))
                        .catch(e => log('Error: ' + e.message));
                }

                function cleanupFiles() {
                    if (confirm('Delete old result files?')) {
                        log('Cleaning up files...');
                        fetch('/api/files/cleanup', {method: 'DELETE'})
                            .then(r => r.json())
                            .then(data => log('Deleted ' + data.deleted + ' files'))
                            .catch(e => log('Error: ' + e.message));
                    }
                }
            </script>
        </body>
        </html>
        `;
        res.send(html);
    }

    // API endpoint implementations
    async getBorrowerCount(req, res) {
        try {
            // Get estimated borrower count
            const count = await this.borrowerService.getTotalBorrowerCount();
            res.json({
                success: true,
                count: count,
                isEstimate: true,
                note: 'Count is estimated based on name sampling due to API search requirements',
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error('Error getting borrower count:', error.message);
            res.status(500).json({
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }

    async getAllBorrowers(req, res) {
        try {
            // Get all borrower contacts with full data
            const contacts = await this.borrowerService.getAllBorrowerContacts();
            res.json({
                success: true,
                contacts: contacts,
                count: contacts.length,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error('Error getting all borrowers:', error.message);
            res.status(500).json({
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }

    async getSampleBorrowers(req, res) {
        try {
            const limit = parseInt(req.params.limit) || 5;
            const contactsResponse = await this.borrowerService.getBorrowerContactIds(limit);

            const borrowers = [];
            for (const contactId of contactsResponse.contactIds.slice(0, Math.min(limit, 5))) {
                try {
                    const details = await this.borrowerService.getBorrowerContactDetails(contactId);
                    borrowers.push({
                        id: details.id,
                        name: `${details.firstName || ''} ${details.lastName || ''}`.trim(),
                        email: details.personalEmail || details.businessEmail,
                        phone: details.homePhone || details.workPhone || details.mobilePhone
                    });
                } catch (error) {
                    console.error(`Error getting details for ${contactId}:`, error.message);
                }
            }

            res.json({
                success: true,
                borrowers,
                total: contactsResponse.totalRecords,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error('Error getting sample borrowers:', error.message);
            res.status(500).json({
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }

    async testDataExtraction(req, res) {
        try {
            const limit = parseInt(req.body.limit) || 3;
            const contactsResponse = await this.borrowerService.getBorrowerContactIds(limit);

            const extractions = [];
            for (const contactId of contactsResponse.contactIds.slice(0, limit)) {
                try {
                    const details = await this.borrowerService.getBorrowerContactDetails(contactId);
                    const comprehensive = this.borrowerService.extractComprehensiveData(details);
                    extractions.push(comprehensive);
                } catch (error) {
                    console.error(`Error extracting data for ${contactId}:`, error.message);
                }
            }

            res.json({
                success: true,
                extractions,
                count: extractions.length,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error('Error testing data extraction:', error.message);
            res.status(500).json({
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }

    async runIntegration(req, res) {
        try {
            this.stats.integrationRuns++;
            const limit = parseInt(req.body.limit) || 10;

            console.log(`Starting integration for ${limit} contacts...`);
            const result = await this.integrationService.runIntegration(limit);

            res.json({
                success: true,
                processed: result.processed,
                errors: result.errors,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            this.stats.errors++;
            console.error('Error running integration:', error.message);
            res.status(500).json({
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }

    getIntegrationStatus(req, res) {
        res.json({
            success: true,
            status: 'ready',
            stats: this.stats,
            timestamp: new Date().toISOString()
        });
    }

    async getIntegrationResults(req, res) {
        try {
            const dataDir = path.join(__dirname, 'data');
            if (!fs.existsSync(dataDir)) {
                return res.json({ success: true, results: [] });
            }

            const files = fs.readdirSync(dataDir)
                .filter(file => file.startsWith('integration-results-'))
                .sort((a, b) => b.localeCompare(a))
                .slice(0, 10);

            const results = files.map(file => {
                try {
                    const content = JSON.parse(fs.readFileSync(path.join(dataDir, file), 'utf8'));
                    return {
                        filename: file,
                        summary: content.summary,
                        timestamp: content.summary.completedAt
                    };
                } catch (error) {
                    return { filename: file, error: 'Could not parse file' };
                }
            });

            res.json({
                success: true,
                results,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error('Error getting integration results:', error.message);
            res.status(500).json({
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }

    async handleWebhook(req, res) {
        try {
            this.stats.webhookEvents++;

            // Parse webhook payload
            const payload = JSON.parse(req.body.toString());
            console.log(`Received webhook: ${payload.eventType} - ${payload.meta?.resourceType}`);

            // Simple webhook handling - just log for now
            res.status(200).json({
                status: 'received',
                eventId: payload.eventId,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            this.stats.errors++;
            console.error('Error handling webhook:', error.message);
            res.status(500).json({
                error: 'Internal server error',
                timestamp: new Date().toISOString()
            });
        }
    }

    testWebhook(req, res) {
        res.json({
            status: 'success',
            message: 'Webhook endpoint is working',
            timestamp: new Date().toISOString()
        });
    }

    getConfiguration(req, res) {
        res.json({
            success: true,
            config: {
                encompassApiUrl: process.env.ENCOMPASS_API_URL ? 'configured' : 'missing',
                encompassClientId: process.env.ENCOMPASS_CLIENT_ID ? 'configured' : 'missing',
                gohighlevelApiKey: process.env.GOHIGHLEVEL_API_KEY ? 'configured' : 'missing',
                gohighlevelLocationId: process.env.GOHIGHLEVEL_LOCATION_ID ? 'configured' : 'missing',
                webhookPort: this.port,
                webhookSigningKey: process.env.ENCOMPASS_WEBHOOK_SIGNING_KEY ? 'configured' : 'missing'
            },
            timestamp: new Date().toISOString()
        });
    }

    async testConfiguration(req, res) {
        try {
            // Test Encompass connection
            const token = await this.borrowerService.encompassAuth.getAccessToken();

            res.json({
                success: true,
                tests: {
                    encompass: token ? 'connected' : 'failed',
                    gohighlevel: this.ghlClient.apiKey ? 'configured' : 'missing'
                },
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }

    listResultFiles(req, res) {
        try {
            const dataDir = path.join(__dirname, 'data');
            if (!fs.existsSync(dataDir)) {
                return res.json({ success: true, files: [] });
            }

            const files = fs.readdirSync(dataDir)
                .filter(file => file.endsWith('.json'))
                .map(file => {
                    const stats = fs.statSync(path.join(dataDir, file));
                    return {
                        name: file,
                        size: stats.size,
                        modified: stats.mtime
                    };
                })
                .sort((a, b) => b.modified - a.modified);

            res.json({
                success: true,
                files,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error('Error listing files:', error.message);
            res.status(500).json({
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }

    getResultFile(req, res) {
        try {
            const filename = req.params.filename;
            const filePath = path.join(__dirname, 'data', filename);

            if (!fs.existsSync(filePath)) {
                return res.status(404).json({
                    success: false,
                    error: 'File not found'
                });
            }

            const content = JSON.parse(fs.readFileSync(filePath, 'utf8'));
            res.json({
                success: true,
                filename,
                content,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error('Error getting file:', error.message);
            res.status(500).json({
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }

    cleanupFiles(req, res) {
        try {
            const dataDir = path.join(__dirname, 'data');
            if (!fs.existsSync(dataDir)) {
                return res.json({ success: true, deleted: 0 });
            }

            const files = fs.readdirSync(dataDir);
            const oldFiles = files.filter(file => {
                const stats = fs.statSync(path.join(dataDir, file));
                const daysSinceModified = (Date.now() - stats.mtime.getTime()) / (1000 * 60 * 60 * 24);
                return daysSinceModified > 7; // Delete files older than 7 days
            });

            oldFiles.forEach(file => {
                fs.unlinkSync(path.join(dataDir, file));
            });

            res.json({
                success: true,
                deleted: oldFiles.length,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error('Error cleaning up files:', error.message);
            res.status(500).json({
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }
    
    formatUptime(ms) {
        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (days > 0) return `${days}d ${hours % 24}h ${minutes % 60}m`;
        if (hours > 0) return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
        if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
        return `${seconds}s`;
    }

    start() {
        this.app.listen(this.port, () => {
            console.log(`[${new Date().toISOString()}] 🚀 Comprehensive Integration Server started on port ${this.port}`);
            console.log(`[${new Date().toISOString()}] 📊 Dashboard: http://localhost:${this.port}`);
            console.log(`[${new Date().toISOString()}] 📡 Webhook: http://localhost:${this.port}/webhook/encompass`);
            console.log(`[${new Date().toISOString()}] 🏥 Health: http://localhost:${this.port}/health`);
        });
    }
}

// Create and start server
const server = new ComprehensiveServer();

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log(`\n[${new Date().toISOString()}] 🛑 Received SIGINT, shutting down gracefully...`);
    process.exit(0);
});

// Start the server
server.start();

module.exports = ComprehensiveServer;
