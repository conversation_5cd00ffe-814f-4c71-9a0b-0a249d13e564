#!/usr/bin/env node

/**
 * Webhook Server Entry Point
 * Starts the Encompass webhook server for real-time borrower notifications
 */

require('dotenv').config();
const WebhookService = require('./services/webhook-service');

// Configuration
const config = {
    port: process.env.WEBHOOK_PORT || 3000,
    signingKey: process.env.ENCOMPASS_WEBHOOK_SIGNING_KEY,
    
    // Encompass API configuration
    baseUrl: process.env.ENCOMPASS_API_URL,
    clientId: process.env.ENCOMPASS_CLIENT_ID,
    clientSecret: process.env.ENCOMPASS_CLIENT_SECRET,
    username: process.env.ENCOMPASS_USERNAME,
    password: process.env.ENCOMPASS_PASSWORD,
    
    // GoHighLevel configuration
    apiKey: process.env.GOHIGHLEVEL_API_KEY,
    locationId: process.env.GOHIGHLEVEL_LOCATION_ID,
    pipelineId: process.env.GOHIGHLEVEL_PIPELINE_ID,
    pipelineStageId: process.env.GOHIGHLEVEL_PIPELINE_STAGE_ID
};

// Validate required configuration
const requiredEnvVars = [
    'ENCOMPASS_API_URL',
    'ENCOMPASS_CLIENT_ID',
    'ENCOMPASS_CLIENT_SECRET',
    'ENCOMPASS_USERNAME',
    'ENCOMPASS_PASSWORD',
    'GOHIGHLEVEL_API_KEY',
    'GOHIGHLEVEL_LOCATION_ID'
];

const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
if (missingVars.length > 0) {
    console.error('❌ Missing required environment variables:');
    missingVars.forEach(varName => console.error(`   - ${varName}`));
    console.error('\nPlease check your .env file and ensure all required variables are set.');
    process.exit(1);
}

// Create and start webhook service
const webhookService = new WebhookService(config);

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log(`\n[${new Date().toISOString()}] 🛑 Received SIGINT, shutting down gracefully...`);
    webhookService.stop();
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log(`\n[${new Date().toISOString()}] 🛑 Received SIGTERM, shutting down gracefully...`);
    webhookService.stop();
    process.exit(0);
});

// Start the server
console.log('🚀 Starting Encompass to GoHighLevel Webhook Server');
console.log('='.repeat(60));
console.log(`📡 Port: ${config.port}`);
console.log(`🔐 Signature verification: ${config.signingKey ? 'Enabled' : 'Disabled (Development mode)'}`);
console.log(`🎯 GoHighLevel Location: ${config.locationId}`);
console.log(`📋 Pipeline: ${config.pipelineId || 'Not configured'}`);
console.log('='.repeat(60));

webhookService.start();
