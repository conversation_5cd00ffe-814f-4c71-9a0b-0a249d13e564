const axios = require('axios');
const fs = require('fs');
const express = require('express');
const crypto = require('crypto');
require('dotenv').config();

// Configuration
const baseUrl = 'https://api.elliemae.com';
const username = process.env.ENCOMPASS_USERNAME;
const password = process.env.ENCOMPASS_PASSWORD;
const clientId = process.env.ENCOMPASS_CLIENT_ID;
const clientSecret = process.env.ENCOMPASS_CLIENT_SECRET;
const instanceId = process.env.ENCOMPASS_INSTANCE_ID;

// GoHighLevel Configuration
const ghlApiKey = process.env.GHL_API_KEY;
const ghlLocationId = process.env.GHL_LOCATION_ID;

console.log('🔍 Fetching ALL Borrower Contacts with Complete Data');
console.log('='.repeat(60));

async function getAccessToken() {
  try {
    console.log('🔐 Getting access token...');
    
    const tokenResponse = await axios.post(`${baseUrl}/oauth2/v1/token`, 
      `grant_type=password&username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`,
      {
        headers: {
          'Authorization': `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`,
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );
    
    console.log('✅ Access token obtained successfully');
    return tokenResponse.data.access_token;
  } catch (error) {
    console.error('❌ Error getting access token:', error.response?.data || error.message);
    throw error;
  }
}

async function fetchAllBorrowerContactsComplete(accessToken) {
  const headers = {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  };

  console.log('\n📋 Fetching ALL Borrower Contacts with Complete Data');
  console.log('-'.repeat(50));

  const results = {
    timestamp: new Date().toISOString(),
    total_contact_ids: 0,
    total_detailed_contacts: 0,
    all_contact_ids: [],
    all_detailed_contacts: [],
    pagination_info: {},
    processing_stats: {},
    errors: []
  };

  try {
    // Step 1: Get ALL contact IDs using pagination
    console.log('1️⃣ Fetching ALL contact IDs...');
    
    let allContactIds = [];
    let currentStart = 1;
    let pageSize = 1000;
    let pageCount = 0;
    let hasMore = true;
    
    while (hasMore) {
      console.log(`📄 Fetching contact IDs page ${pageCount + 1} (start: ${currentStart}, limit: ${pageSize})`);
      
      const pageResponse = await axios.post(
        `${baseUrl}/encompass/v1/borrowerContactSelector`,
        { start: currentStart, limit: pageSize },
        { headers }
      );
      
      let pageContacts = [];
      if (Array.isArray(pageResponse.data)) {
        pageContacts = pageResponse.data;
      }
      
      console.log(`   📈 Found ${pageContacts.length} contact IDs on page ${pageCount + 1}`);
      
      if (pageContacts.length > 0) {
        const contactIds = pageContacts.map(contact => contact.id);
        allContactIds.push(...contactIds);
        
        if (pageContacts.length < pageSize) {
          console.log(`   🏁 Last page reached (${pageContacts.length} < ${pageSize})`);
          hasMore = false;
        } else {
          currentStart += pageSize;
          pageCount++;
        }
      } else {
        hasMore = false;
      }
      
      // Add delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    results.total_contact_ids = allContactIds.length;
    results.all_contact_ids = allContactIds;
    results.pagination_info = {
      pages_fetched: pageCount + 1,
      total_contact_ids: allContactIds.length,
      contacts_per_page: pageSize
    };
    
    console.log(`✅ Contact ID collection complete: ${allContactIds.length} total contact IDs`);
    
    // Step 2: Fetch detailed information for ALL contacts
    console.log(`\n2️⃣ Fetching detailed information for ALL ${allContactIds.length} contacts...`);
    
    let detailedContacts = [];
    let processedCount = 0;
    let errorCount = 0;
    const batchSize = 50; // Process in batches to show progress
    
    for (let i = 0; i < allContactIds.length; i++) {
      const contactId = allContactIds[i];
      
      try {
        const detailResponse = await axios.get(
          `${baseUrl}/encompass/v1/borrowerContacts/${contactId}`,
          { headers }
        );
        
        detailedContacts.push(detailResponse.data);
        processedCount++;
        
        // Show progress every batch
        if (processedCount % batchSize === 0 || processedCount === allContactIds.length) {
          const percentage = Math.round((processedCount / allContactIds.length) * 100);
          console.log(`   📊 Progress: ${processedCount}/${allContactIds.length} (${percentage}%) - Errors: ${errorCount}`);
        }
        
        // Add delay to avoid rate limiting (smaller delay for individual requests)
        await new Promise(resolve => setTimeout(resolve, 200));
        
      } catch (error) {
        errorCount++;
        console.error(`   ❌ Error fetching contact ${contactId}: ${error.response?.status}`);
        results.errors.push({
          contact_id: contactId,
          error: error.response?.data || error.message,
          status: error.response?.status
        });
        
        // Continue processing even if some contacts fail
      }
    }
    
    results.total_detailed_contacts = detailedContacts.length;
    results.all_detailed_contacts = detailedContacts;
    results.processing_stats = {
      total_processed: processedCount,
      successful: detailedContacts.length,
      errors: errorCount,
      success_rate: Math.round((detailedContacts.length / allContactIds.length) * 100)
    };
    
    console.log(`✅ Detailed contact fetch complete: ${detailedContacts.length}/${allContactIds.length} successful`);

  } catch (error) {
    console.error('❌ Error in main process:', error.response?.status, error.response?.data || error.message);
    results.errors.push({
      process: 'main',
      error: error.response?.data || error.message,
      status: error.response?.status
    });
  }

  return results;
}

async function main() {
  try {
    const accessToken = await getAccessToken();
    const results = await fetchAllBorrowerContactsComplete(accessToken);
    
    // Save results to multiple files
    const timestamp = Date.now();
    
    // 1. Save summary (without full contact data)
    const summaryFilename = `data/borrower-contacts-all-summary-${timestamp}.json`;
    const summary = {
      timestamp: results.timestamp,
      total_contact_ids: results.total_contact_ids,
      total_detailed_contacts: results.total_detailed_contacts,
      pagination_info: results.pagination_info,
      processing_stats: results.processing_stats,
      errors: results.errors,
      data_files: {
        contact_ids: `borrower-contacts-all-ids-${timestamp}.json`,
        detailed_contacts: `borrower-contacts-all-detailed-${timestamp}.json`
      }
    };
    fs.writeFileSync(summaryFilename, JSON.stringify(summary, null, 2));
    
    // 2. Save all contact IDs
    const idsFilename = `data/borrower-contacts-all-ids-${timestamp}.json`;
    fs.writeFileSync(idsFilename, JSON.stringify({
      timestamp: results.timestamp,
      total_count: results.total_contact_ids,
      contact_ids: results.all_contact_ids
    }, null, 2));
    
    // 3. Save all detailed contacts
    const detailedFilename = `data/borrower-contacts-all-detailed-${timestamp}.json`;
    fs.writeFileSync(detailedFilename, JSON.stringify({
      timestamp: results.timestamp,
      total_count: results.total_detailed_contacts,
      contacts: results.all_detailed_contacts
    }, null, 2));
    
    console.log('\n📊 COMPLETE BORROWER CONTACTS EXTRACTION');
    console.log('='.repeat(50));
    console.log(`✅ Total contact IDs found: ${results.total_contact_ids}`);
    console.log(`✅ Detailed contacts fetched: ${results.total_detailed_contacts}`);
    console.log(`❌ Errors encountered: ${results.errors.length}`);
    console.log(`📊 Success rate: ${results.processing_stats.success_rate || 0}%`);
    
    console.log('\n💾 FILES SAVED:');
    console.log(`   📋 Summary: ${summaryFilename}`);
    console.log(`   🆔 Contact IDs: ${idsFilename}`);
    console.log(`   📇 Detailed Contacts: ${detailedFilename}`);
    
    if (results.pagination_info.pages_fetched) {
      console.log(`\n🔄 PAGINATION INFO:`);
      console.log(`   Pages fetched: ${results.pagination_info.pages_fetched}`);
      console.log(`   Contacts per page: ${results.pagination_info.contacts_per_page}`);
    }
    
    if (results.all_detailed_contacts.length > 0) {
      const sampleContact = results.all_detailed_contacts[0];
      console.log(`\n📋 SAMPLE CONTACT FIELDS (${Object.keys(sampleContact).length} fields):`);
      console.log(`   ${Object.keys(sampleContact).join(', ')}`);
    }
    
    if (results.errors.length > 0) {
      console.log(`\n❌ ERRORS (showing first 5):`);
      results.errors.slice(0, 5).forEach((error, index) => {
        console.log(`   ${index + 1}. ${error.contact_id || error.process}: ${error.status}`);
      });
      if (results.errors.length > 5) {
        console.log(`   ... and ${results.errors.length - 5} more errors`);
      }
    }
    
    console.log('\n🎯 READY FOR GOHIGHLEVEL INTEGRATION!');
    console.log(`   📇 ${results.total_detailed_contacts} borrower contacts available`);
    console.log(`   📋 Rich contact data with names, emails, phones, addresses`);
    console.log(`   🔄 Pagination working for ongoing sync`);
    
  } catch (error) {
    console.error('💥 Fatal error:', error.message);
    process.exit(1);
  }
}

// Run the complete extraction
main();
