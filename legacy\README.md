# Legacy Files

This folder contains legacy integration files that are kept for reference.

## Files

- `encompass-to-gohighlevel-integration.js` - Original integration script

## Usage

To run the legacy integration:

```bash
npm run legacy
```

## Migration

The functionality from these files has been reorganized into:

- `src/` - Organized source code
- `server.js` - Comprehensive server with dashboard
- `src/services/` - Service classes
- `src/utils/` - Utility classes
- `src/scripts/` - Standalone scripts

Use the new organized structure for all new development.
