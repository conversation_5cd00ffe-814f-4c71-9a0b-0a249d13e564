const EncompassApi = require('./encompassApi');
const GoHighLevelApi = require('./gohighlevelApi');
const leadDatabase = require('./leadDatabase');
const logger = require('./logger');

class SyncService {
  constructor() {
    this.encompassApi = new EncompassApi();
    this.gohighlevelApi = new GoHighLevelApi();
    this.lastSyncTime = null;
  }

  async syncLeads() {
    try {
      logger.info('Starting lead synchronization...');

      // Load lead database
      const db = leadDatabase.load();

      // Get recent leads from Encompass
      const leads = await this.encompassApi.getRecentLeads(this.lastSyncTime);

      if (leads.length === 0) {
        logger.info('No new leads found in Encompass');
        return {
          processed: 0,
          created: 0,
          updated: 0,
          errors: 0,
          skipped: 0,
          opportunities: 0
        };
      }

      const results = {
        processed: 0,
        created: 0,
        updated: 0,
        errors: 0,
        skipped: 0,
        opportunities: 0
      };

      // Filter out already processed leads
      const unprocessedLeads = [];
      const alreadyProcessedLeads = [];

      for (const lead of leads) {
        if (leadDatabase.isLeadProcessed(db, lead.id)) {
          const processedInfo = leadDatabase.getProcessedLeadInfo(db, lead.id);
          alreadyProcessedLeads.push({
            id: lead.id,
            name: `${lead.firstName} ${lead.lastName}`,
            ghlContactId: processedInfo.ghlContactId,
            processedAt: processedInfo.processedAt
          });
          results.skipped++;
        } else {
          unprocessedLeads.push(lead);
        }
      }

      // Log duplicate prevention stats
      if (alreadyProcessedLeads.length > 0) {
        logger.info(`🔄 Duplicate Prevention:`);
        logger.info(`  - Already processed: ${alreadyProcessedLeads.length} leads`);
        logger.info(`  - New to process: ${unprocessedLeads.length} leads`);
      } else {
        logger.info(`✨ All ${unprocessedLeads.length} leads are new and will be processed.`);
      }

      // Process each unprocessed lead
      for (const lead of unprocessedLeads) {
        try {
          const leadResult = await this.processLead(lead, db);
          results.processed++;

          if (leadResult.action === 'created') {
            results.created++;
          } else if (leadResult.action === 'updated') {
            results.updated++;
          }

          if (leadResult.opportunityCreated) {
            results.opportunities++;
          }
        } catch (error) {
          logger.error(`Failed to process lead ${lead.id}`, error);
          leadDatabase.markLeadFailed(db, lead.id, error.message);
          results.errors++;
        }
      }

      // Save updated database
      leadDatabase.save(db);

      // Update last sync time
      this.lastSyncTime = new Date().toISOString();

      logger.info('Lead synchronization completed', results);
      return results;
    } catch (error) {
      logger.error('Failed to sync leads', error);
      throw error;
    }
  }

  async processLead(lead, db) {
    // Skip leads without essential information (we'll auto-generate email/phone if missing)
    if (!lead.firstName && !lead.lastName) {
      logger.warn(`Skipping lead ${lead.id} - no name provided`);
      throw new Error('No name provided');
    }

    // First, try to find existing contact by Encompass ID
    let existingContact = await this.gohighlevelApi.findContactByEncompassId(lead.id);

    // If not found by Encompass ID, try by email (only if email exists and is valid)
    if (!existingContact && lead.email && this.gohighlevelApi.validateAndCleanEmail(lead.email)) {
      existingContact = await this.gohighlevelApi.findContactByEmail(lead.email);
    }

    let contactId = null;
    let isNewContact = false;
    let action = '';
    let opportunityCreated = false;

    if (existingContact) {
      // Update existing contact
      const updateResult = await this.gohighlevelApi.updateContact(existingContact.id, lead);
      if (updateResult.success) {
        contactId = existingContact.id;
        action = 'updated';
        logger.info(`Updated existing contact for lead ${lead.id} (Contact ID: ${contactId})`);
      } else {
        logger.error(`Failed to update contact for lead ${lead.id}: ${updateResult.error}`);
        throw new Error(`Failed to update contact: ${updateResult.error}`);
      }
    } else {
      // Create new contact
      const createResult = await this.gohighlevelApi.createContact(lead);
      if (createResult.success) {
        contactId = createResult.contact.id;
        isNewContact = true;
        action = 'created';
        logger.info(`Created new contact for lead ${lead.id} (Contact ID: ${contactId})`);
      } else {
        logger.error(`Failed to create contact for lead ${lead.id}: ${createResult.error}`);
        throw new Error(`Failed to create contact: ${createResult.error}`);
      }
    }

    let opportunityId = null;

    // Create opportunity if contact was processed successfully
    if (contactId) {
      try {
        // Check if contact already has an opportunity (for existing contacts)
        const hasOpportunity = !isNewContact ? await this.gohighlevelApi.hasExistingOpportunity(contactId) : false;

        if (!hasOpportunity) {
          const opportunityResult = await this.gohighlevelApi.createOpportunity(contactId, lead);
          if (opportunityResult.success) {
            opportunityId = opportunityResult.opportunity.id;
            opportunityCreated = true;
            logger.info(`Created opportunity for lead ${lead.id} (Contact ID: ${contactId}, Opportunity ID: ${opportunityId})`);
          } else if (opportunityResult.reason !== 'missing_pipeline_config') {
            logger.warn(`Failed to create opportunity for lead ${lead.id}: ${opportunityResult.error}`);
          }
        } else {
          logger.info(`Contact ${contactId} already has an opportunity, skipping opportunity creation`);
        }
      } catch (error) {
        logger.warn(`Error checking/creating opportunity for lead ${lead.id}: ${error.message}`);
      }
    }

    // Mark lead as processed in database
    leadDatabase.markLeadProcessed(db, lead.id, contactId, action, opportunityId);

    return {
      action: action,
      contactId: contactId,
      opportunityId: opportunityId,
      opportunityCreated: opportunityCreated
    };
  }

  async testConnection() {
    try {
      logger.info('Testing API connections...');
      
      // Test Encompass connection
      await this.encompassApi.authenticate();
      logger.info('✓ Encompass API connection successful');
      
      // Test GoHighLevel connection by attempting to fetch contacts
      await this.gohighlevelApi.findContactByEmail('<EMAIL>');
      logger.info('✓ GoHighLevel API connection successful');
      
      return true;
    } catch (error) {
      logger.error('API connection test failed', error);
      return false;
    }
  }
}

module.exports = SyncService;
