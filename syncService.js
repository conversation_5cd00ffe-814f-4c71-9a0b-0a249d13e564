const EncompassApi = require('./encompassApi');
const GoHighLevelApi = require('./gohighlevelApi');
const logger = require('./logger');

class SyncService {
  constructor() {
    this.encompassApi = new EncompassApi();
    this.gohighlevelApi = new GoHighLevelApi();
    this.lastSyncTime = null;
  }

  async syncLeads() {
    try {
      logger.info('Starting lead synchronization...');
      
      // Get recent leads from Encompass
      const leads = await this.encompassApi.getRecentLeads(this.lastSyncTime);
      
      if (leads.length === 0) {
        logger.info('No new leads found in Encompass');
        return { processed: 0, created: 0, updated: 0, errors: 0 };
      }

      const results = {
        processed: leads.length,
        created: 0,
        updated: 0,
        errors: 0
      };

      // Process each lead
      for (const lead of leads) {
        try {
          await this.processLead(lead, results);
        } catch (error) {
          logger.error(`Failed to process lead ${lead.id}`, error);
          results.errors++;
        }
      }

      // Update last sync time
      this.lastSyncTime = new Date().toISOString();
      
      logger.info('Lead synchronization completed', results);
      return results;
    } catch (error) {
      logger.error('Failed to sync leads', error);
      throw error;
    }
  }

  async processLead(lead, results) {
    // Skip leads without essential information
    if (!lead.email && !lead.phone) {
      logger.warn(`Skipping lead ${lead.id} - no email or phone provided`);
      return;
    }

    // First, try to find existing contact by Encompass ID
    let existingContact = await this.gohighlevelApi.findContactByEncompassId(lead.id);
    
    // If not found by Encompass ID, try by email
    if (!existingContact && lead.email) {
      existingContact = await this.gohighlevelApi.findContactByEmail(lead.email);
    }

    if (existingContact) {
      // Update existing contact
      await this.gohighlevelApi.updateContact(existingContact.id, lead);
      results.updated++;
      logger.info(`Updated existing contact for lead ${lead.id}`);
    } else {
      // Create new contact
      await this.gohighlevelApi.createContact(lead);
      results.created++;
      logger.info(`Created new contact for lead ${lead.id}`);
    }
  }

  async testConnection() {
    try {
      logger.info('Testing API connections...');
      
      // Test Encompass connection
      await this.encompassApi.authenticate();
      logger.info('✓ Encompass API connection successful');
      
      // Test GoHighLevel connection by attempting to fetch contacts
      await this.gohighlevelApi.findContactByEmail('<EMAIL>');
      logger.info('✓ GoHighLevel API connection successful');
      
      return true;
    } catch (error) {
      logger.error('API connection test failed', error);
      return false;
    }
  }
}

module.exports = SyncService;
