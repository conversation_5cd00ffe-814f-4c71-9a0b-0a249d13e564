#!/usr/bin/env node

/**
 * Get Total Borrower Count Script
 * Simple script to get the total number of borrowers in Encompass
 */

require('dotenv').config();
const BorrowerService = require('../services/borrower-service');

async function main() {
    console.log('🚀 Encompass Borrower Count Tool');
    console.log('='.repeat(50));

    try {
        // Initialize borrower service
        const borrowerService = new BorrowerService();

        // Get total count
        const totalCount = await borrowerService.getTotalBorrowerCount();

        console.log('📊 Results:');
        console.log('='.repeat(50));
        console.log(`📥 Total Borrower Contacts: ${totalCount.toLocaleString()}`);
        console.log(`📅 Retrieved at: ${new Date().toLocaleString()}`);
        console.log('='.repeat(50));

        // Additional statistics if needed
        if (totalCount > 0) {
            console.log('💡 Additional Information:');
            console.log(`   • Average per day (last 30 days): ~${Math.round(totalCount / 30).toLocaleString()}`);
            console.log(`   • Average per week: ~${Math.round(totalCount / 52).toLocaleString()}`);
            console.log(`   • Average per month: ~${Math.round(totalCount / 12).toLocaleString()}`);
        }

        console.log('\n✅ Count retrieval completed successfully!');

    } catch (error) {
        console.error('❌ Error getting borrower count:', error.message);
        process.exit(1);
    }
}

// Run the script
if (require.main === module) {
    main();
}

module.exports = main;
