const axios = require('axios');
const fs = require('fs');
require('dotenv').config();

// Configuration
const baseUrl = 'https://api.elliemae.com';
const username = process.env.ENCOMPASS_USERNAME;
const password = process.env.ENCOMPASS_PASSWORD;
const clientId = process.env.ENCOMPASS_CLIENT_ID;
const clientSecret = process.env.ENCOMPASS_CLIENT_SECRET;
const instanceId = process.env.ENCOMPASS_INSTANCE_ID;

console.log('🔍 Testing Encompass Borrower Contacts API with Pagination');
console.log('='.repeat(60));

async function getAccessToken() {
  try {
    console.log('🔐 Getting access token...');
    
    const tokenResponse = await axios.post(`${baseUrl}/oauth2/v1/token`, 
      `grant_type=password&username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`,
      {
        headers: {
          'Authorization': `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`,
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );
    
    console.log('✅ Access token obtained successfully');
    return tokenResponse.data.access_token;
  } catch (error) {
    console.error('❌ Error getting access token:', error.response?.data || error.message);
    throw error;
  }
}

async function testBorrowerContactsWithPagination(accessToken) {
  const headers = {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  };

  console.log('\n📋 Testing Borrower Contacts API Endpoints');
  console.log('-'.repeat(50));

  const results = {
    timestamp: new Date().toISOString(),
    endpoints_tested: [],
    total_contacts_found: 0,
    pagination_details: {},
    errors: []
  };

  // Test 1: V1 Get Borrower Contact List (POST with pagination)
  console.log('\n1️⃣ Testing V1 Get Borrower Contact List (POST with pagination)');
  try {
    const contactListPayload = {
      start: 0,
      limit: 50,
      // Add any filters if needed
      // filter: {}
    };

    const contactListResponse = await axios.post(
      `${baseUrl}/encompass/v1/borrowerContactSelector/`,
      contactListPayload,
      { headers }
    );

    console.log(`✅ Borrower Contact List - Status: ${contactListResponse.status}`);
    console.log(`📊 Response data type: ${typeof contactListResponse.data}`);

    if (contactListResponse.data && contactListResponse.data.contacts && Array.isArray(contactListResponse.data.contacts)) {
      console.log(`📈 Found ${contactListResponse.data.contacts.length} contacts in first page`);
      results.total_contacts_found += contactListResponse.data.contacts.length;

      // Test pagination by requesting more pages
      let currentStart = 50;
      let hasMore = contactListResponse.data.contacts.length === 50;
      let pageCount = 1;

      while (hasMore && pageCount < 5) { // Limit to 5 pages for testing
        console.log(`📄 Fetching page ${pageCount + 1} (start: ${currentStart})`);

        const nextPagePayload = {
          start: currentStart,
          limit: 50
        };

        const nextPageResponse = await axios.post(
          `${baseUrl}/encompass/v1/borrowerContactSelector/`,
          nextPagePayload,
          { headers }
        );

        if (nextPageResponse.data && nextPageResponse.data.contacts && Array.isArray(nextPageResponse.data.contacts)) {
          console.log(`📈 Page ${pageCount + 1}: Found ${nextPageResponse.data.contacts.length} contacts`);
          results.total_contacts_found += nextPageResponse.data.contacts.length;
          hasMore = nextPageResponse.data.contacts.length === 50;
          currentStart += 50;
          pageCount++;
        } else {
          hasMore = false;
        }

        // Add delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      results.pagination_details.borrower_contact_list = {
        pages_tested: pageCount,
        total_contacts: results.total_contacts_found,
        pagination_working: pageCount > 1
      };

    } else if (Array.isArray(contactListResponse.data)) {
      console.log(`📈 Found ${contactListResponse.data.length} contacts in first page (direct array)`);
      results.total_contacts_found += contactListResponse.data.length;

      // Test pagination by requesting more pages
      let currentStart = 50;
      let hasMore = contactListResponse.data.length === 50;
      let pageCount = 1;

      while (hasMore && pageCount < 5) { // Limit to 5 pages for testing
        console.log(`📄 Fetching page ${pageCount + 1} (start: ${currentStart})`);

        const nextPagePayload = {
          start: currentStart,
          limit: 50
        };

        const nextPageResponse = await axios.post(
          `${baseUrl}/encompass/v1/borrowerContactSelector/`,
          nextPagePayload,
          { headers }
        );

        if (nextPageResponse.data && Array.isArray(nextPageResponse.data)) {
          console.log(`📈 Page ${pageCount + 1}: Found ${nextPageResponse.data.length} contacts`);
          results.total_contacts_found += nextPageResponse.data.length;
          hasMore = nextPageResponse.data.length === 50;
          currentStart += 50;
          pageCount++;
        } else {
          hasMore = false;
        }

        // Add delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      results.pagination_details.borrower_contact_list = {
        pages_tested: pageCount,
        total_contacts: results.total_contacts_found,
        pagination_working: pageCount > 1
      };

    } else {
      console.log(`📋 Response structure:`, Object.keys(contactListResponse.data || {}));
    }

    results.endpoints_tested.push({
      endpoint: 'V1 Get Borrower Contact List (POST)',
      url: '/encompass/v1/borrowerContactSelector/',
      status: 'success',
      response_size: contactListResponse.data?.length || 0
    });

  } catch (error) {
    console.error('❌ Error testing Borrower Contact List:', error.response?.status, error.response?.data || error.message);
    results.errors.push({
      endpoint: 'V1 Get Borrower Contact List (POST)',
      error: error.response?.data || error.message,
      status: error.response?.status
    });
  }

  // Test 2: Try to get a specific borrower contact (if we have any IDs)
  console.log('\n2️⃣ Testing V1 Get a Borrower Contact (GET)');
  try {
    // First, let's try to get a list to find a contact ID
    const sampleContactResponse = await axios.post(
      `${baseUrl}/encompass/v1/borrowerContactSelector/`,
      { start: 0, limit: 1 },
      { headers }
    );

    if (sampleContactResponse.data && Array.isArray(sampleContactResponse.data) && sampleContactResponse.data.length > 0) {
      const contactId = sampleContactResponse.data[0].id;
      console.log(`🔍 Testing with contact ID: ${contactId}`);
      
      const specificContactResponse = await axios.get(
        `${baseUrl}/encompass/v1/borrowerContacts/${contactId}`,
        { headers }
      );
      
      console.log(`✅ Specific Borrower Contact - Status: ${specificContactResponse.status}`);
      console.log(`📋 Contact details keys:`, Object.keys(specificContactResponse.data || {}));
      
      results.endpoints_tested.push({
        endpoint: 'V1 Get a Borrower Contact (GET)',
        url: `/encompass/v1/borrowerContacts/${contactId}`,
        status: 'success',
        contact_id: contactId
      });
    } else {
      console.log('⚠️ No contacts found to test specific contact endpoint');
    }

  } catch (error) {
    console.error('❌ Error testing specific Borrower Contact:', error.response?.status, error.response?.data || error.message);
    results.errors.push({
      endpoint: 'V1 Get a Borrower Contact (GET)',
      error: error.response?.data || error.message,
      status: error.response?.status
    });
  }

  // Test 3: Test the pagination endpoint specifically mentioned by user
  console.log('\n3️⃣ Testing V1 View Borrower Contacts (with Pagination) - Advanced');
  try {
    // First, let's try without specifying fields to see what's available
    const basicPaginationPayload = {
      start: 0,
      limit: 25
    };

    const basicPaginationResponse = await axios.post(
      `${baseUrl}/encompass/v1/borrowerContactSelector`,
      basicPaginationPayload,
      { headers }
    );

    console.log(`✅ Basic Advanced Pagination - Status: ${basicPaginationResponse.status}`);
    console.log(`📊 Response structure:`, Object.keys(basicPaginationResponse.data || {}));

    if (basicPaginationResponse.data) {
      const data = basicPaginationResponse.data;
      console.log(`📈 Contacts in response: ${Array.isArray(data) ? data.length : data.contacts?.length || 'N/A'}`);

      // If it's an array, show sample contact structure
      if (Array.isArray(data) && data.length > 0) {
        console.log(`📋 Sample contact fields:`, Object.keys(data[0]));

        // Test pagination with cursor if available
        if (data.length === 25) {
          console.log(`🔄 Testing next page...`);
          const nextPagePayload = {
            start: 25,
            limit: 25
          };

          const nextPageResponse = await axios.post(
            `${baseUrl}/encompass/v1/borrowerContactSelector`,
            nextPagePayload,
            { headers }
          );

          if (nextPageResponse.data && Array.isArray(nextPageResponse.data)) {
            console.log(`📈 Next page: Found ${nextPageResponse.data.length} contacts`);
            results.pagination_details.advanced_pagination = {
              first_page_count: data.length,
              second_page_count: nextPageResponse.data.length,
              pagination_working: true,
              sample_fields: Object.keys(data[0])
            };
          }
        }
      } else if (data.contacts) {
        console.log(`🔄 Cursor provided: ${data.cursor ? 'Yes' : 'No'}`);
        console.log(`📄 Has more pages: ${data.hasMore || 'N/A'}`);

        results.pagination_details.advanced_pagination = {
          contacts_count: data.contacts?.length || 0,
          has_cursor: !!data.cursor,
          has_more: data.hasMore,
          cursor_type: data.cursorType || 'N/A'
        };
      }
    }

    results.endpoints_tested.push({
      endpoint: 'V1 View Borrower Contacts (with Pagination)',
      url: '/encompass/v1/borrowerContactSelector',
      status: 'success',
      advanced_pagination: true
    });

  } catch (error) {
    console.error('❌ Error testing Advanced Pagination:', error.response?.status, error.response?.data || error.message);
    results.errors.push({
      endpoint: 'V1 View Borrower Contacts (with Pagination)',
      error: error.response?.data || error.message,
      status: error.response?.status
    });
  }

  return results;
}

async function main() {
  try {
    const accessToken = await getAccessToken();
    const results = await testBorrowerContactsWithPagination(accessToken);
    
    // Save results to file
    const filename = `data/borrower-contacts-pagination-test-${Date.now()}.json`;
    fs.writeFileSync(filename, JSON.stringify(results, null, 2));
    
    console.log('\n📊 TEST SUMMARY');
    console.log('='.repeat(50));
    console.log(`✅ Endpoints tested: ${results.endpoints_tested.length}`);
    console.log(`❌ Errors encountered: ${results.errors.length}`);
    console.log(`📈 Total contacts found: ${results.total_contacts_found}`);
    console.log(`💾 Results saved to: ${filename}`);
    
    if (results.pagination_details.borrower_contact_list) {
      const paginationInfo = results.pagination_details.borrower_contact_list;
      console.log(`\n🔄 PAGINATION TEST RESULTS:`);
      console.log(`   Pages tested: ${paginationInfo.pages_tested}`);
      console.log(`   Pagination working: ${paginationInfo.pagination_working ? '✅ YES' : '❌ NO'}`);
    }
    
    if (results.errors.length > 0) {
      console.log('\n❌ ERRORS:');
      results.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error.endpoint}: ${error.status} - ${JSON.stringify(error.error)}`);
      });
    }
    
  } catch (error) {
    console.error('💥 Fatal error:', error.message);
    process.exit(1);
  }
}

// Run the test
main();
