const axios = require('axios');
const fs = require('fs');
const path = require('path');
const tokenManager = require('./encompass-token-manager');
require('dotenv').config();

// Configuration
const baseUrl = 'https://api.elliemae.com';
const dataDir = path.join(__dirname, 'data');

// Ensure data directory exists
if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
}

class BorrowerContactsFetcher {
    constructor() {
        this.totalFetched = 0;
        this.currentPage = 1;
        this.allContacts = [];
        this.startTime = Date.now();
    }

    // Fetch borrower contacts using pagination API
    async fetchContactsPage(start = 1, limit = 1000, cursorType = 'randomAccess') {
        try {
            const token = await tokenManager.getValidToken();
            
            console.log(`📄 Fetching page starting at ${start} (limit: ${limit})`);
            
            const requestBody = {
                start: start.toString(),
                limit: limit.toString(),
                cursorType: cursorType
            };

            const response = await axios.post(
                `${baseUrl}/encompass/v1/borrowerContactSelector`,
                requestBody,
                {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );

            const data = response.data;
            console.log(`✅ Fetched ${data.length} contacts from page starting at ${start}`);
            
            return {
                contacts: data,
                hasMore: data.length === limit, // If we got full page, there might be more
                nextStart: start + data.length
            };

        } catch (error) {
            if (error.response?.status === 401) {
                console.log('🔄 Token expired, refreshing...');
                await tokenManager.refreshToken();
                return this.fetchContactsPage(start, limit, cursorType);
            }
            
            console.error(`❌ Error fetching contacts page at ${start}:`, error.response?.data || error.message);
            throw error;
        }
    }

    // Fetch detailed information for a specific contact
    async fetchContactDetails(contactId) {
        try {
            const token = await tokenManager.getValidToken();
            
            const response = await axios.get(
                `${baseUrl}/encompass/v1/borrowerContacts/${contactId}`,
                {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );

            return response.data;
        } catch (error) {
            if (error.response?.status === 401) {
                console.log('🔄 Token expired, refreshing...');
                await tokenManager.refreshToken();
                return this.fetchContactDetails(contactId);
            }
            
            console.error(`❌ Error fetching contact details for ${contactId}:`, error.response?.data || error.message);
            return null;
        }
    }

    // Fetch all contacts with pagination
    async fetchAllContacts(options = {}) {
        const {
            maxPages = null,
            pageSize = 1000,
            saveProgress = true,
            fetchDetails = false,
            startFrom = 1
        } = options;

        console.log('🚀 Starting to fetch all borrower contacts');
        console.log('='.repeat(60));
        console.log(`📊 Configuration:`);
        console.log(`   - Page size: ${pageSize}`);
        console.log(`   - Max pages: ${maxPages || 'unlimited'}`);
        console.log(`   - Starting from: ${startFrom}`);
        console.log(`   - Fetch details: ${fetchDetails}`);
        console.log(`   - Save progress: ${saveProgress}`);
        console.log('='.repeat(60));

        let currentStart = startFrom;
        let pageCount = 0;
        let hasMore = true;

        while (hasMore && (maxPages === null || pageCount < maxPages)) {
            try {
                // Fetch page
                const pageResult = await this.fetchContactsPage(currentStart, pageSize);
                
                if (pageResult.contacts.length === 0) {
                    console.log('📄 No more contacts found, stopping pagination');
                    break;
                }

                // Process contacts
                for (const contact of pageResult.contacts) {
                    let contactData = contact;
                    
                    // Fetch detailed information if requested
                    if (fetchDetails && contact.id) {
                        console.log(`🔍 Fetching details for contact: ${contact.id}`);
                        const details = await this.fetchContactDetails(contact.id);
                        if (details) {
                            contactData = { ...contact, ...details };
                        }
                        
                        // Rate limiting for detailed fetches
                        await this.delay(100);
                    }
                    
                    this.allContacts.push(contactData);
                    this.totalFetched++;
                }

                pageCount++;
                currentStart = pageResult.nextStart;
                hasMore = pageResult.hasMore;

                // Progress update
                const elapsed = (Date.now() - this.startTime) / 1000;
                console.log(`📈 Progress: ${this.totalFetched} contacts, ${pageCount} pages, ${elapsed.toFixed(1)}s elapsed`);

                // Save progress periodically
                if (saveProgress && pageCount % 10 === 0) {
                    await this.saveProgress();
                }

                // Rate limiting between pages
                await this.delay(500);

            } catch (error) {
                console.error(`❌ Error on page ${pageCount + 1}:`, error.message);
                
                // Save progress before potentially stopping
                if (saveProgress) {
                    await this.saveProgress();
                }
                
                // Decide whether to continue or stop
                if (error.response?.status >= 500) {
                    console.log('⚠️ Server error, waiting 5 seconds before retry...');
                    await this.delay(5000);
                    continue;
                } else {
                    throw error;
                }
            }
        }

        // Final save
        if (saveProgress) {
            await this.saveProgress();
        }

        const totalTime = (Date.now() - this.startTime) / 1000;
        console.log('\n🎉 Fetch completed!');
        console.log('='.repeat(60));
        console.log(`📊 Final Statistics:`);
        console.log(`   - Total contacts: ${this.totalFetched}`);
        console.log(`   - Pages processed: ${pageCount}`);
        console.log(`   - Total time: ${totalTime.toFixed(1)} seconds`);
        console.log(`   - Average per page: ${(totalTime / pageCount).toFixed(1)} seconds`);
        console.log('='.repeat(60));

        return this.allContacts;
    }

    // Save current progress to file
    async saveProgress() {
        try {
            const timestamp = Date.now();
            const filename = `borrower-contacts-paginated-${timestamp}.json`;
            const filepath = path.join(dataDir, filename);

            const progressData = {
                metadata: {
                    totalContacts: this.totalFetched,
                    currentPage: this.currentPage,
                    fetchedAt: new Date().toISOString(),
                    elapsedTime: (Date.now() - this.startTime) / 1000
                },
                contacts: this.allContacts
            };

            fs.writeFileSync(filepath, JSON.stringify(progressData, null, 2));
            console.log(`💾 Progress saved: ${filename} (${this.totalFetched} contacts)`);
            
            // Also save a summary file
            const summaryFilename = `borrower-contacts-summary-${timestamp}.json`;
            const summaryFilepath = path.join(dataDir, summaryFilename);
            
            const summary = {
                totalContacts: this.totalFetched,
                sampleContacts: this.allContacts.slice(0, 5),
                fieldAnalysis: this.analyzeFields()
            };
            
            fs.writeFileSync(summaryFilepath, JSON.stringify(summary, null, 2));
            console.log(`📋 Summary saved: ${summaryFilename}`);

        } catch (error) {
            console.error('❌ Error saving progress:', error.message);
        }
    }

    // Analyze fields in the contacts
    analyzeFields() {
        if (this.allContacts.length === 0) return {};

        const fieldCounts = {};
        const sampleContact = this.allContacts[0];

        Object.keys(sampleContact).forEach(field => {
            fieldCounts[field] = this.allContacts.filter(contact => 
                contact[field] !== null && contact[field] !== undefined && contact[field] !== ''
            ).length;
        });

        return {
            totalFields: Object.keys(sampleContact).length,
            fieldPopulation: fieldCounts,
            populationPercentages: Object.fromEntries(
                Object.entries(fieldCounts).map(([field, count]) => [
                    field, 
                    ((count / this.allContacts.length) * 100).toFixed(1) + '%'
                ])
            )
        };
    }

    // Utility delay function
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// CLI usage
if (require.main === module) {
    const args = process.argv.slice(2);
    
    const options = {
        maxPages: null,
        pageSize: 1000,
        saveProgress: true,
        fetchDetails: false,
        startFrom: 1
    };

    // Parse command line arguments
    for (let i = 0; i < args.length; i += 2) {
        const flag = args[i];
        const value = args[i + 1];

        switch (flag) {
            case '--max-pages':
                options.maxPages = parseInt(value);
                break;
            case '--page-size':
                options.pageSize = parseInt(value);
                break;
            case '--start-from':
                options.startFrom = parseInt(value);
                break;
            case '--fetch-details':
                options.fetchDetails = value === 'true';
                break;
            case '--no-save':
                options.saveProgress = false;
                i--; // No value for this flag
                break;
        }
    }

    async function runFetch() {
        try {
            const fetcher = new BorrowerContactsFetcher();
            await fetcher.fetchAllContacts(options);
        } catch (error) {
            console.error('❌ Fetch failed:', error.message);
            process.exit(1);
        }
    }

    console.log('📖 Usage examples:');
    console.log('  node fetch-borrower-contacts-paginated.js');
    console.log('  node fetch-borrower-contacts-paginated.js --max-pages 5');
    console.log('  node fetch-borrower-contacts-paginated.js --page-size 500 --fetch-details true');
    console.log('  node fetch-borrower-contacts-paginated.js --start-from 5000 --max-pages 10');
    console.log('');

    runFetch();
}

module.exports = BorrowerContactsFetcher;
