#!/usr/bin/env node

/**
 * Fetch All Borrowers Script
 * 
 * This script fetches all borrower contacts from Encompass API
 * and saves them to a JSON file for analysis and backup purposes.
 */

const fs = require('fs');
const path = require('path');
const EncompassAuth = require('./src/utils/encompass-auth');
require('dotenv').config();

class BorrowerFetcher {
    constructor() {
        this.baseUrl = process.env.ENCOMPASS_API_URL || 'https://api.elliemae.com';

        // Use the same authentication approach as borrower service
        this.encompassAuth = new EncompassAuth({
            baseUrl: this.baseUrl,
            clientId: process.env.ENCOMPASS_CLIENT_ID,
            clientSecret: process.env.ENCOMPASS_CLIENT_SECRET,
            username: process.env.ENCOMPASS_USERNAME,
            password: process.env.ENCOMPASS_PASSWORD
        });
    }



    /**
     * Get borrower contacts from Encompass API
     */
    async getBorrowerContacts(limit = 100, start = 1) {
        try {
            const url = `${this.baseUrl}/encompass/v1/borrowerContactSelector?start=${start}&limit=${limit}`;

            const response = await this.encompassAuth.makeApiCall(
                url,
                {
                    method: 'POST',
                    data: {
                        fields: [
                            "Contact.FirstName",
                            "Contact.LastName",
                            "Contact.PersonalEmail",
                            "Contact.HomePhone",
                            "Contact.MobilePhone",
                            "Contact.WorkPhone"
                        ]
                    }
                },
                `Getting ${limit} borrower contacts starting from ${start}`
            );

            return {
                contacts: response.borrowerContacts || [],
                totalRecords: response.totalRecords || 0,
                currentStart: start,
                currentLimit: limit,
                hasMore: (start + limit - 1) < (response.totalRecords || 0)
            };

        } catch (error) {
            console.error(`[${new Date().toISOString()}] ❌ Error getting borrower contacts:`, error.response?.data || error.message);
            throw error;
        }
    }

    /**
     * Fetch all borrower contacts with pagination
     */
    async fetchAllContacts() {
        try {
            console.log(`[${new Date().toISOString()}] 🚀 Starting to fetch all borrower contacts...`);
            
            const allContacts = [];
            let start = 1;
            const limit = 1000; // Max limit per request
            let hasMore = true;
            let totalRecords = 0;

            while (hasMore) {
                const response = await this.getBorrowerContacts(limit, start);
                
                if (response.contacts && response.contacts.length > 0) {
                    allContacts.push(...response.contacts);
                    console.log(`[${new Date().toISOString()}] 📊 Fetched ${response.contacts.length} contacts (${allContacts.length}/${response.totalRecords} total)`);
                }

                totalRecords = response.totalRecords;
                hasMore = response.hasMore;
                start += limit;

                // Rate limiting - wait 1 second between requests
                if (hasMore) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            }

            console.log(`[${new Date().toISOString()}] ✅ Completed fetching all contacts: ${allContacts.length}/${totalRecords}`);
            
            return {
                contacts: allContacts,
                totalRecords: totalRecords,
                fetchedAt: new Date().toISOString(),
                summary: {
                    totalFetched: allContacts.length,
                    totalAvailable: totalRecords,
                    completionPercentage: totalRecords > 0 ? ((allContacts.length / totalRecords) * 100).toFixed(2) : 0
                }
            };

        } catch (error) {
            console.error(`[${new Date().toISOString()}] ❌ Error fetching all contacts:`, error.message);
            throw error;
        }
    }

    /**
     * Save contacts to JSON file
     */
    async saveToFile(data, filename = null) {
        try {
            // Create data directory if it doesn't exist
            const dataDir = path.join(__dirname, 'data');
            if (!fs.existsSync(dataDir)) {
                fs.mkdirSync(dataDir, { recursive: true });
            }

            // Generate filename if not provided
            if (!filename) {
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                filename = `borrower-contacts-${timestamp}.json`;
            }

            const filePath = path.join(dataDir, filename);
            
            // Save with pretty formatting
            fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
            
            console.log(`[${new Date().toISOString()}] 💾 Saved ${data.contacts.length} contacts to: ${filePath}`);
            console.log(`[${new Date().toISOString()}] 📁 File size: ${(fs.statSync(filePath).size / 1024).toFixed(2)} KB`);
            
            return filePath;

        } catch (error) {
            console.error(`[${new Date().toISOString()}] ❌ Error saving to file:`, error.message);
            throw error;
        }
    }

    /**
     * Main execution function
     */
    async run() {
        try {
            console.log(`[${new Date().toISOString()}] 🎯 Encompass Borrower Fetcher Started`);
            console.log(`[${new Date().toISOString()}] 🔗 API URL: ${this.baseUrl}`);
            console.log(`[${new Date().toISOString()}] 👤 Username: ${this.encompassAuth.username}`);
            
            // Fetch all contacts
            const result = await this.fetchAllContacts();
            
            // Save to file
            const filePath = await this.saveToFile(result);
            
            // Print summary
            console.log(`\n📋 FETCH SUMMARY:`);
            console.log(`   Total Contacts Fetched: ${result.summary.totalFetched}`);
            console.log(`   Total Available: ${result.summary.totalAvailable}`);
            console.log(`   Completion: ${result.summary.completionPercentage}%`);
            console.log(`   File: ${filePath}`);
            console.log(`   Timestamp: ${result.fetchedAt}`);
            
            return result;

        } catch (error) {
            console.error(`[${new Date().toISOString()}] ❌ Script failed:`, error.message);
            process.exit(1);
        }
    }
}

// Run the script if called directly
if (require.main === module) {
    const fetcher = new BorrowerFetcher();
    fetcher.run().then(() => {
        console.log(`[${new Date().toISOString()}] 🎉 Script completed successfully!`);
        process.exit(0);
    }).catch((error) => {
        console.error(`[${new Date().toISOString()}] 💥 Script failed:`, error.message);
        process.exit(1);
    });
}

module.exports = BorrowerFetcher;
