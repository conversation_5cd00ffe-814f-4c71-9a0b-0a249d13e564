# Encompass Webhook Setup Guide

This guide explains how to set up External Users webhooks for real-time borrower detection.

## 🚨 **Important: Domain Allow-list Requirement**

**Before creating webhooks, your domain must be added to the ICE allow-list.**

### For Development (ngrok)
If you're using ngrok for development, the domain needs to be allowlisted by ICE.

### For Production
Your production domain must be submitted to ICE for allowlisting.

## 📋 **Prerequisites**

### 1. **Domain Allow-list Request**
Submit a request to ICE to add your domain to the allow-list:
- **Documentation**: [Adding Webhook Destination Domain to ICE Allow-list](https://ice-developer-hub.readme.io/developer-connect/docs/adding-webhook-destination-domain-to-the-ice-allow-list)
- **Contact**: ICE Support or your account representative

### 2. **Technical Requirements**
- ✅ **HTTPS endpoint** with CA-signed certificate
- ✅ **TLS 1.2+** support (TLS 1.3 recommended)
- ✅ **30-second response time** requirement
- ✅ **Publicly accessible** endpoint

### 3. **Environment Configuration**
Update your `.env` file:

```env
# Webhook Configuration
WEBHOOK_ENDPOINT=https://your-domain.com
ENCOMPASS_WEBHOOK_SIGNING_KEY=optional_signing_key
```

## 🔧 **Setup Steps**

### Step 1: Configure Environment
```bash
# Edit .env file
WEBHOOK_ENDPOINT=https://daa4b7009e15.ngrok-free.app
```

### Step 2: Start Your Server
```bash
npm run dev
```

### Step 3: Create Webhook Subscription
```bash
npm run setup-webhook
```

## 📊 **Expected Output**

### Successful Setup:
```
🔗 Encompass External Users Webhook Setup
============================================================
🔧 Creating External Users Webhook Subscription:
============================================================
Endpoint: https://your-domain.com/webhook/encompass
Signing Key: Not configured
✅ URL format is valid
⚠️ WARNING: Using development domain: daa4b7009e15.ngrok-free.app
⚠️ For production, ensure your domain is added to ICE allow-list
[2025-07-16T23:45:28.614Z] Getting Encompass access token...
[2025-07-16T23:45:29.974Z] ✅ Encompass access token obtained successfully
[2025-07-16T23:45:29.975Z] 🌐 Creating External Users webhook subscription
✅ External Users webhook subscription created successfully!
Subscription ID: abc123-def456-ghi789
Resource: ExternalUsers
Events: create, update, delete
Endpoint: https://your-domain.com/webhook/encompass
```

### Failed Setup (Domain Not Allowlisted):
```
❌ Creating External Users webhook subscription failed: { message: 'bad endpoint URL - error Invalid URL' }

🔍 TROUBLESHOOTING TIPS:
1. Ensure your webhook endpoint uses HTTPS
2. Verify the domain is added to ICE allow-list
3. Check that the URL is publicly accessible
4. Ensure TLS 1.2+ is supported
```

## 🛠 **Troubleshooting**

### Error: "bad endpoint URL - error Invalid URL"

**Most Common Cause**: Domain not on ICE allow-list

**Solutions**:
1. **Submit allow-list request** to ICE for your domain
2. **Use a different domain** that's already allowlisted
3. **Contact ICE support** for assistance

### Error: Connection timeout or SSL issues

**Possible Causes**:
- Self-signed certificates (not supported)
- TLS version too old
- Endpoint not publicly accessible

**Solutions**:
1. Use CA-signed certificates
2. Ensure TLS 1.2+ support
3. Test endpoint accessibility from external networks

## 🧪 **Testing Your Webhook**

### 1. Test Endpoint Accessibility
```bash
curl -X POST https://your-domain.com/webhook/encompass \
  -H "Content-Type: application/json" \
  -d '{"test": "webhook"}'
```

### 2. Test Webhook Processing
```bash
npm run test-webhook
```

### 3. Monitor Server Logs
```bash
npm run dev
# Watch for webhook events in console
```

## 📝 **Webhook Payload Structure**

When an External User is created, Encompass sends:

```json
{
  "eventId": "b7cb3e9c-2481-4c9c-af90-55a5d1b6a693",
  "eventTime": "2025-07-16T21:00:00.000Z",
  "eventType": "create",
  "meta": {
    "userId": "admin",
    "resourceType": "ExternalUsers",
    "resourceId": "423253b1-cd23-4424-b6e8-204dfce2751e",
    "instanceId": "BE11140034",
    "resourceRef": "/encompass/v3/externalUsers",
    "payload": {
      "entities": [
        {
          "id": "test-borrower-001"
        }
      ]
    }
  }
}
```

## 🔄 **Integration Flow**

1. **External User Created** in Encompass
2. **Webhook Sent** to your endpoint
3. **User ID Extracted** from payload
4. **Borrower Data Fetched** from Encompass APIs
5. **Data Processed** and saved to JSON
6. **GoHighLevel Sync** (future enhancement)

## 📞 **Support**

### ICE Support
- **Allow-list requests**: Submit through official channels
- **Technical issues**: Contact ICE Developer Support
- **Documentation**: [Encompass Developer Connect](https://developer.icemortgagetechnology.com/)

### Development Issues
- Check server logs for detailed error messages
- Verify environment configuration
- Test endpoint accessibility
- Review webhook payload format

## 🚀 **Production Deployment**

### Before Going Live:
1. ✅ Domain added to ICE allow-list
2. ✅ Production HTTPS endpoint with CA-signed certificate
3. ✅ TLS 1.3 support configured
4. ✅ Webhook endpoint tested and responding < 30 seconds
5. ✅ Error handling and logging implemented
6. ✅ GoHighLevel integration configured

### Monitoring:
- Set up alerts for webhook failures
- Monitor response times
- Log all webhook events for debugging
- Implement retry logic for failed processing

This setup enables real-time borrower detection and processing for your Encompass-GoHighLevel integration.
