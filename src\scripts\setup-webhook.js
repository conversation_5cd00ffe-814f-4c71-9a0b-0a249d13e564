#!/usr/bin/env node

/**
 * Encompass Webhook Setup Script
 * Helps configure webhook subscriptions in Encompass for real-time borrower notifications
 */

require('dotenv').config();
const EncompassAuth = require('../utils/encompass-auth');

class WebhookSetup {
    constructor(config = {}) {
        this.encompassAuth = new EncompassAuth(config);
        this.baseUrl = config.baseUrl || process.env.ENCOMPASS_API_URL;
        this.webhookEndpoint = config.webhookEndpoint || process.env.WEBHOOK_ENDPOINT;
        this.signingKey = config.signingKey || process.env.ENCOMPASS_WEBHOOK_SIGNING_KEY;
    }

    /**
     * List all webhook resources available in Encompass
     */
    async listResources() {
        try {
            console.log('📋 Available Webhook Resources in Encompass:');
            console.log('='.repeat(60));

            const response = await this.encompassAuth.makeApiCall(
                `${this.baseUrl}/webhook/v1/resources`,
                { method: 'GET' },
                'Getting webhook resources'
            );

            if (response && Array.isArray(response)) {
                response.forEach((resource, index) => {
                    console.log(`${index + 1}. ${resource.resource}`);
                    if (resource.events && Array.isArray(resource.events)) {
                        resource.events.forEach(event => {
                            console.log(`   - ${event}`);
                        });
                    }
                    console.log('');
                });
            }

            return response;

        } catch (error) {
            console.error('❌ Error listing webhook resources:', error.response?.data || error.message);
            throw error;
        }
    }

    /**
     * List existing webhook subscriptions
     */
    async listSubscriptions() {
        try {
            console.log('📋 Existing Webhook Subscriptions:');
            console.log('='.repeat(60));

            const response = await this.encompassAuth.makeApiCall(
                `${this.baseUrl}/webhook/v1/subscriptions`,
                { method: 'GET' },
                'Getting webhook subscriptions'
            );

            if (response && Array.isArray(response)) {
                if (response.length === 0) {
                    console.log('No webhook subscriptions found.');
                } else {
                    response.forEach((subscription, index) => {
                        console.log(`${index + 1}. Subscription ID: ${subscription.subscriptionId}`);
                        console.log(`   Endpoint: ${subscription.endpoint?.uri}`);
                        console.log(`   Status: ${subscription.status}`);
                        console.log(`   Events: ${subscription.events?.length || 0}`);
                        if (subscription.events) {
                            subscription.events.forEach(event => {
                                console.log(`     - ${event.resource} (${event.eventType})`);
                            });
                        }
                        console.log('');
                    });
                }
            }

            return response;

        } catch (error) {
            console.error('❌ Error listing webhook subscriptions:', error.response?.data || error.message);
            throw error;
        }
    }

    /**
     * Create a webhook subscription for External Users (borrower) events
     */
    async createExternalUsersWebhook() {
        try {
            if (!this.webhookEndpoint) {
                throw new Error('WEBHOOK_ENDPOINT environment variable is required');
            }

            console.log('🔧 Creating External Users Webhook Subscription:');
            console.log('='.repeat(60));
            console.log(`Endpoint: ${this.webhookEndpoint}/webhook/external-users`);
            console.log(`Signing Key: ${this.signingKey ? 'Configured' : 'Not configured'}`);

            const subscriptionData = {
                events: [
                    {
                        resource: "ExternalUsers",
                        eventType: "create"
                    },
                    {
                        resource: "ExternalUsers",
                        eventType: "update"
                    },
                    {
                        resource: "ExternalUsers",
                        eventType: "delete"
                    }
                ],
                endpoint: {
                    uri: `${this.webhookEndpoint}/webhook/external-users`,
                    headers: {
                        "Content-Type": "application/json"
                    }
                }
            };

            // Add signing key if configured
            if (this.signingKey) {
                subscriptionData.signingKey = this.signingKey;
            }

            const response = await this.encompassAuth.makeApiCall(
                `${this.baseUrl}/webhook/v1/subscriptions`,
                {
                    method: 'POST',
                    data: subscriptionData
                },
                'Creating External Users webhook subscription'
            );

            console.log('✅ External Users webhook subscription created successfully!');
            console.log(`Subscription ID: ${response.subscriptionId}`);
            console.log(`Resource: ExternalUsers`);
            console.log(`Events: create, update, delete`);
            console.log(`Endpoint: ${this.webhookEndpoint}/webhook/external-users`);

            return response;

        } catch (error) {
            console.error('❌ Error creating External Users webhook:', error.response?.data || error.message);
            throw error;
        }
    }

    /**
     * Create a webhook subscription for borrower contacts events (legacy)
     */
    async createBorrowerWebhook() {
        try {
            if (!this.webhookEndpoint) {
                throw new Error('WEBHOOK_ENDPOINT environment variable is required');
            }

            console.log('🔧 Creating Borrower Contacts Webhook Subscription:');
            console.log('='.repeat(60));
            console.log(`Endpoint: ${this.webhookEndpoint}/webhook/encompass`);
            console.log(`Signing Key: ${this.signingKey ? 'Configured' : 'Not configured'}`);

            const subscriptionData = {
                events: [
                    {
                        resource: "urn:elli:encompass:borrowerContacts",
                        eventType: "create"
                    },
                    {
                        resource: "urn:elli:encompass:borrowerContacts",
                        eventType: "update"
                    }
                ],
                endpoint: {
                    uri: `${this.webhookEndpoint}/webhook/encompass`,
                    headers: {
                        "Content-Type": "application/json"
                    }
                }
            };

            // Add signing key if configured
            if (this.signingKey) {
                subscriptionData.signingKey = this.signingKey;
            }

            const response = await this.encompassAuth.makeApiCall(
                `${this.baseUrl}/webhook/v1/subscriptions`,
                {
                    method: 'POST',
                    data: subscriptionData
                },
                'Creating borrower contacts webhook subscription'
            );

            console.log('✅ Borrower Contacts webhook subscription created successfully!');
            console.log(`Subscription ID: ${response.subscriptionId}`);
            console.log(`Status: ${response.status}`);

            return response;

        } catch (error) {
            console.error('❌ Error creating webhook subscription:', error.response?.data || error.message);
            throw error;
        }
    }

    /**
     * Delete a webhook subscription
     */
    async deleteSubscription(subscriptionId) {
        try {
            console.log(`🗑️ Deleting webhook subscription: ${subscriptionId}`);

            await this.encompassAuth.makeApiCall(
                `${this.baseUrl}/webhook/v1/subscriptions/${subscriptionId}`,
                { method: 'DELETE' },
                `Deleting webhook subscription ${subscriptionId}`
            );

            console.log('✅ Webhook subscription deleted successfully!');

        } catch (error) {
            console.error('❌ Error deleting webhook subscription:', error.response?.data || error.message);
            throw error;
        }
    }

    /**
     * Test webhook endpoint connectivity
     */
    async testWebhookEndpoint() {
        try {
            if (!this.webhookEndpoint) {
                throw new Error('WEBHOOK_ENDPOINT environment variable is required');
            }

            console.log('🧪 Testing Webhook Endpoint Connectivity:');
            console.log('='.repeat(60));
            console.log(`Testing: ${this.webhookEndpoint}`);

            const axios = require('axios');
            
            // Try to reach the health endpoint
            const healthUrl = this.webhookEndpoint.replace('/webhook/encompass', '/health');
            
            const response = await axios.get(healthUrl, { timeout: 5000 });
            
            console.log('✅ Webhook endpoint is reachable!');
            console.log(`Status: ${response.status}`);
            console.log(`Response:`, response.data);

            return true;

        } catch (error) {
            console.error('❌ Webhook endpoint test failed:', error.message);
            console.log('💡 Make sure your webhook server is running and accessible from the internet');
            return false;
        }
    }
}

async function main() {
    const command = process.argv[2] || 'help';
    const webhookSetup = new WebhookSetup();

    console.log('🔗 Encompass Webhook Setup Tool');
    console.log('='.repeat(60));

    try {
        switch (command.toLowerCase()) {
            case 'resources':
                await webhookSetup.listResources();
                break;

            case 'list':
                await webhookSetup.listSubscriptions();
                break;

            case 'create':
                await webhookSetup.createExternalUsersWebhook();
                break;

            case 'create-borrower':
                await webhookSetup.createBorrowerWebhook();
                break;

            case 'delete':
                const subscriptionId = process.argv[3];
                if (!subscriptionId) {
                    console.error('❌ Please provide a subscription ID to delete');
                    console.log('Usage: npm run setup-webhook delete <subscription-id>');
                    process.exit(1);
                }
                await webhookSetup.deleteSubscription(subscriptionId);
                break;

            case 'test':
                await webhookSetup.testWebhookEndpoint();
                break;

            case 'help':
            default:
                console.log('Available commands:');
                console.log('  resources       - List available webhook resources');
                console.log('  list            - List existing webhook subscriptions');
                console.log('  create          - Create External Users webhook subscription (recommended)');
                console.log('  create-borrower - Create borrower contacts webhook subscription (legacy)');
                console.log('  delete          - Delete a webhook subscription');
                console.log('  test            - Test webhook endpoint connectivity');
                console.log('');
                console.log('Usage examples:');
                console.log('  npm run setup-webhook resources');
                console.log('  npm run setup-webhook list');
                console.log('  npm run setup-webhook create          # Create External Users webhook');
                console.log('  npm run setup-webhook create-borrower # Create borrower contacts webhook');
                console.log('  npm run setup-webhook delete <subscription-id>');
                console.log('  npm run setup-webhook test');
                console.log('');
                console.log('Environment variables required:');
                console.log('  WEBHOOK_ENDPOINT - Your webhook endpoint URL');
                console.log('  ENCOMPASS_WEBHOOK_SIGNING_KEY - Optional signing key');
                break;
        }

    } catch (error) {
        console.error('❌ Command failed:', error.message);
        process.exit(1);
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = WebhookSetup;
