#!/usr/bin/env node

/**
 * Encompass Webhook Setup Script
 * Helps configure webhook subscriptions in Encompass for real-time borrower notifications
 */

require('dotenv').config();
const EncompassAuth = require('../utils/encompass-auth');

class WebhookSetup {
    constructor(config = {}) {
        this.encompassAuth = new EncompassAuth(config);
        this.baseUrl = config.baseUrl || process.env.ENCOMPASS_API_URL;
        this.webhookEndpoint = config.webhookEndpoint || process.env.WEBHOOK_ENDPOINT;
        this.signingKey = config.signingKey || process.env.ENCOMPASS_WEBHOOK_SIGNING_KEY;
    }

    /**
     * List all webhook resources available in Encompass
     */
    async listResources() {
        try {
            console.log('📋 Available Webhook Resources in Encompass:');
            console.log('='.repeat(60));

            const response = await this.encompassAuth.makeApiCall(
                `${this.baseUrl}/webhook/v1/resources`,
                { method: 'GET' },
                'Getting webhook resources'
            );

            if (response && Array.isArray(response)) {
                response.forEach((resource, index) => {
                    console.log(`${index + 1}. ${resource.resource}`);
                    if (resource.events && Array.isArray(resource.events)) {
                        resource.events.forEach(event => {
                            console.log(`   - ${event}`);
                        });
                    }
                    console.log('');
                });
            }

            return response;

        } catch (error) {
            console.error('❌ Error listing webhook resources:', error.response?.data || error.message);
            throw error;
        }
    }

    /**
     * List existing webhook subscriptions
     */
    async listSubscriptions() {
        try {
            console.log('📋 Existing Webhook Subscriptions:');
            console.log('='.repeat(60));

            const response = await this.encompassAuth.makeApiCall(
                `${this.baseUrl}/webhook/v1/subscriptions`,
                { method: 'GET' },
                'Getting webhook subscriptions'
            );

            if (response && Array.isArray(response)) {
                if (response.length === 0) {
                    console.log('No webhook subscriptions found.');
                } else {
                    response.forEach((subscription, index) => {
                        console.log(`${index + 1}. Subscription ID: ${subscription.subscriptionId}`);
                        console.log(`   Endpoint: ${subscription.endpoint?.uri}`);
                        console.log(`   Status: ${subscription.status}`);
                        console.log(`   Events: ${subscription.events?.length || 0}`);
                        if (subscription.events) {
                            subscription.events.forEach(event => {
                                console.log(`     - ${event.resource} (${event.eventType})`);
                            });
                        }
                        console.log('');
                    });
                }
            }

            return response;

        } catch (error) {
            console.error('❌ Error listing webhook subscriptions:', error.response?.data || error.message);
            throw error;
        }
    }

    /**
     * Create a webhook subscription for External Users (borrower) events
     */
    async createExternalUsersWebhook() {
        try {
            const webhookUrl = `${this.webhookEndpoint}/webhook/encompass`;

            console.log('🔧 Creating External Users Webhook Subscription:');
            console.log('='.repeat(60));
            console.log(`Endpoint: ${webhookUrl}`);
            console.log(`Signing Key: ${this.signingKey ? 'Configured' : 'Not configured'}`);

            // Validate URL format
            try {
                new URL(webhookUrl);
                console.log('✅ URL format is valid');
            } catch (urlError) {
                console.error('❌ Invalid URL format:', urlError.message);
                throw new Error(`Invalid webhook URL format: ${webhookUrl}`);
            }

            // Check if URL is HTTPS
            if (!webhookUrl.startsWith('https://')) {
                console.warn('⚠️ WARNING: Webhook URL should use HTTPS for production');
                console.warn('⚠️ Encompass requires HTTPS endpoints with CA-signed certificates');
            }

            // Check if domain might need to be allowlisted
            const domain = new URL(webhookUrl).hostname;
            if (domain.includes('ngrok') || domain.includes('localhost')) {
                console.warn('⚠️ WARNING: Using development domain:', domain);
                console.warn('⚠️ For production, ensure your domain is added to ICE allow-list');
                console.warn('⚠️ See: https://ice-developer-hub.readme.io/developer-connect/docs/adding-webhook-destination-domain-to-the-ice-allow-list');
            }

            const subscriptionData = {
                events: [
                    {
                        resource: "ExternalUsers",
                        eventType: "create"
                    },
                    {
                        resource: "ExternalUsers",
                        eventType: "update"
                    },
                    {
                        resource: "ExternalUsers",
                        eventType: "delete"
                    }
                ],
                endpoint: {
                    uri: webhookUrl,
                    headers: {
                        "Content-Type": "application/json"
                    }
                }
            };

            // Add signing key if configured
            if (this.signingKey) {
                subscriptionData.signingKey = this.signingKey;
            }

            console.log('📋 Subscription payload:');
            console.log(JSON.stringify(subscriptionData, null, 2));

            const response = await this.encompassAuth.makeApiCall(
                `${this.baseUrl}/webhook/v1/subscriptions`,
                {
                    method: 'POST',
                    data: subscriptionData
                },
                'Creating External Users webhook subscription'
            );

            console.log('✅ External Users webhook subscription created successfully!');
            console.log(`Subscription ID: ${response.subscriptionId}`);
            console.log(`Resource: ExternalUsers`);
            console.log(`Events: create, update, delete`);
            console.log(`Endpoint: ${webhookUrl}`);

            return response;

        } catch (error) {
            console.error('❌ Error creating External Users webhook:', error.response?.data || error.message);

            // Provide helpful troubleshooting information
            if (error.response?.data?.message?.includes('Invalid URL')) {
                console.log('\n🔍 TROUBLESHOOTING TIPS:');
                console.log('1. Ensure your webhook endpoint uses HTTPS');
                console.log('2. Verify the domain is added to ICE allow-list');
                console.log('3. Check that the URL is publicly accessible');
                console.log('4. Ensure TLS 1.2+ is supported');
                console.log('\n📖 Documentation:');
                console.log('https://developer.icemortgagetechnology.com/developer-connect/reference/webhook-endpoints');
            }

            throw error;
        }
    }

    /**
     * Create a webhook subscription for borrower contacts events (legacy)
     */
    async createBorrowerWebhook() {
        try {
            if (!this.webhookEndpoint) {
                throw new Error('WEBHOOK_ENDPOINT environment variable is required');
            }

            console.log('🔧 Creating Borrower Contacts Webhook Subscription:');
            console.log('='.repeat(60));
            console.log(`Endpoint: ${this.webhookEndpoint}/webhook/encompass`);
            console.log(`Signing Key: ${this.signingKey ? 'Configured' : 'Not configured'}`);

            const subscriptionData = {
                events: [
                    {
                        resource: "urn:elli:encompass:borrowerContacts",
                        eventType: "create"
                    },
                    {
                        resource: "urn:elli:encompass:borrowerContacts",
                        eventType: "update"
                    }
                ],
                endpoint: {
                    uri: `${this.webhookEndpoint}/webhook/encompass`,
                    headers: {
                        "Content-Type": "application/json"
                    }
                }
            };

            // Add signing key if configured
            if (this.signingKey) {
                subscriptionData.signingKey = this.signingKey;
            }

            const response = await this.encompassAuth.makeApiCall(
                `${this.baseUrl}/webhook/v1/subscriptions`,
                {
                    method: 'POST',
                    data: subscriptionData
                },
                'Creating borrower contacts webhook subscription'
            );

            console.log('✅ Borrower Contacts webhook subscription created successfully!');
            console.log(`Subscription ID: ${response.subscriptionId}`);
            console.log(`Status: ${response.status}`);

            return response;

        } catch (error) {
            console.error('❌ Error creating webhook subscription:', error.response?.data || error.message);
            throw error;
        }
    }

    /**
     * Delete a webhook subscription
     */
    async deleteSubscription(subscriptionId) {
        try {
            console.log(`🗑️ Deleting webhook subscription: ${subscriptionId}`);

            await this.encompassAuth.makeApiCall(
                `${this.baseUrl}/webhook/v1/subscriptions/${subscriptionId}`,
                { method: 'DELETE' },
                `Deleting webhook subscription ${subscriptionId}`
            );

            console.log('✅ Webhook subscription deleted successfully!');

        } catch (error) {
            console.error('❌ Error deleting webhook subscription:', error.response?.data || error.message);
            throw error;
        }
    }

    /**
     * Test webhook endpoint connectivity
     */
    async testWebhookEndpoint() {
        try {
            if (!this.webhookEndpoint) {
                throw new Error('WEBHOOK_ENDPOINT environment variable is required');
            }

            console.log('🧪 Testing Webhook Endpoint Connectivity:');
            console.log('='.repeat(60));
            console.log(`Testing: ${this.webhookEndpoint}`);

            const axios = require('axios');
            
            // Try to reach the health endpoint
            const healthUrl = this.webhookEndpoint.replace('/webhook/encompass', '/health');
            
            const response = await axios.get(healthUrl, { timeout: 5000 });
            
            console.log('✅ Webhook endpoint is reachable!');
            console.log(`Status: ${response.status}`);
            console.log(`Response:`, response.data);

            return true;

        } catch (error) {
            console.error('❌ Webhook endpoint test failed:', error.message);
            console.log('💡 Make sure your webhook server is running and accessible from the internet');
            return false;
        }
    }
}

async function main() {
    const webhookSetup = new WebhookSetup();

    console.log('🔗 Encompass External Users Webhook Setup');
    console.log('='.repeat(60));

    try {
        // Simply create the External Users webhook using environment variables
        await webhookSetup.createExternalUsersWebhook();

    } catch (error) {
        console.error('❌ Command failed:', error.message);
        process.exit(1);
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = WebhookSetup;
