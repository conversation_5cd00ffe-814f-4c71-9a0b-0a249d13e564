{
  "timestamp": "2025-07-04T17:57:54.797Z",
  "total_contacts_processed": 5,
  "detailed_contacts": [
    {
      "employerName": "",
      "birthdate": "1967-09-08T00:00:00Z",
      "borrowerType": 1,
      "primaryEmail": "                                                  ",
      "primaryPhone": "",
      "referral": "Guillermo <PERSON> realtor",
      "firstName": "BARBARA",
      "lastName": "HERRERA VEGA",
      "ownerId": "abejerano",
      "accessLevel": 0,
      "currentMailingAddress": {
        "city": "Opa Locka",
        "state": "FL",
        "street1": "2430 NW 140th St",
        "street2": "",
        "zip": "33054-4059",
        "unitType": ""
      },
      "bizAddress": {
        "city": "",
        "state": "",
        "street1": "",
        "street2": "",
        "zip": "",
        "unitType": ""
      },
      "businessWebUrl": null,
      "jobTitle": "",
      "workPhone": "************",
      "homePhone": "************",
      "mobilePhone": "",
      "faxNumber": "",
      "personalEmail": "<EMAIL>",
      "businessEmail": "",
      "salutation": "",
      "id": "a26af9f3-2d41-4ae1-9a41-366437ff8aaa"
    },
    {
      "employerName": "",
      "birthdate": "1986-12-28T00:00:00Z",
      "borrowerType": 1,
      "primaryEmail": "                                                  ",
      "primaryPhone": "",
      "referral": "Tony realtor",
      "firstName": "ORLANDO",
      "lastName": "GIL",
      "ownerId": "abejerano",
      "accessLevel": 0,
      "currentMailingAddress": {
        "city": "MIAMI",
        "state": "FL",
        "street1": "14736 SW 55TH TERR",
        "street2": "",
        "zip": "33185",
        "unitType": ""
      },
      "bizAddress": {
        "city": "",
        "state": "",
        "street1": "",
        "street2": "",
        "zip": "",
        "unitType": ""
      },
      "businessWebUrl": null,
      "jobTitle": "",
      "workPhone": "************",
      "homePhone": "************",
      "mobilePhone": "************",
      "faxNumber": "",
      "personalEmail": "<EMAIL>",
      "businessEmail": "",
      "salutation": "",
      "id": "7daa9d4a-5411-4a47-8650-0bf2946a5a4d"
    },
    {
      "employerName": "",
      "birthdate": "1980-06-12T00:00:00Z",
      "borrowerType": 1,
      "primaryEmail": "                                                  ",
      "primaryPhone": "",
      "referral": "Julita realtor",
      "firstName": "ARIANA",
      "lastName": "RODRIGUEZ",
      "ownerId": "abejerano",
      "accessLevel": 0,
      "currentMailingAddress": {
        "city": "Miami",
        "state": "FL",
        "street1": "14525 SW 170 ST",
        "street2": "",
        "zip": "33177",
        "unitType": ""
      },
      "bizAddress": {
        "city": "",
        "state": "",
        "street1": "",
        "street2": "",
        "zip": "",
        "unitType": ""
      },
      "businessWebUrl": null,
      "jobTitle": "",
      "workPhone": "************",
      "homePhone": "************",
      "mobilePhone": "",
      "faxNumber": "",
      "personalEmail": "<EMAIL>",
      "businessEmail": "",
      "salutation": "",
      "id": "95671b80-e118-4aa8-aaf9-cd55db29dbff"
    },
    {
      "employerName": "",
      "birthdate": "1966-01-16T00:00:00Z",
      "borrowerType": 1,
      "primaryEmail": "                                                  ",
      "primaryPhone": "",
      "referral": "Tony realtor",
      "firstName": "MANUEL",
      "lastName": "JUANES",
      "ownerId": "abejerano",
      "accessLevel": 0,
      "currentMailingAddress": {
        "city": "Miami",
        "state": "FL",
        "street1": "13237 SW 46 Ln",
        "street2": "",
        "zip": "33175",
        "unitType": ""
      },
      "bizAddress": {
        "city": "",
        "state": "",
        "street1": "",
        "street2": "",
        "zip": "",
        "unitType": ""
      },
      "businessWebUrl": null,
      "jobTitle": "",
      "workPhone": "************",
      "homePhone": "************",
      "mobilePhone": "",
      "faxNumber": "",
      "personalEmail": "<EMAIL>",
      "businessEmail": "",
      "salutation": "",
      "id": "609bf4b4-c530-4d0f-bfa5-c5278fc5173d"
    },
    {
      "employerName": "",
      "birthdate": "1960-01-08T00:00:00Z",
      "borrowerType": 1,
      "primaryEmail": "                                                  ",
      "primaryPhone": "",
      "referral": "alex bejerano",
      "firstName": "TONY",
      "lastName": "MEDEROS",
      "ownerId": "abejerano",
      "accessLevel": 0,
      "currentMailingAddress": {
        "city": "Hialeah",
        "state": "FL",
        "street1": "610 E 24th ST",
        "street2": "",
        "zip": "33013",
        "unitType": ""
      },
      "bizAddress": {
        "city": "",
        "state": "",
        "street1": "",
        "street2": "",
        "zip": "",
        "unitType": ""
      },
      "businessWebUrl": null,
      "jobTitle": "",
      "workPhone": "************",
      "homePhone": "************",
      "mobilePhone": "",
      "faxNumber": "",
      "personalEmail": "<EMAIL>",
      "businessEmail": "",
      "salutation": "",
      "id": "380a9e11-7207-410c-b5a7-d9277439cccf"
    }
  ],
  "sample_contact_structure": {
    "fields": [
      "employerName",
      "birthdate",
      "borrowerType",
      "primaryEmail",
      "primaryPhone",
      "referral",
      "firstName",
      "lastName",
      "ownerId",
      "accessLevel",
      "currentMailingAddress",
      "bizAddress",
      "businessWebUrl",
      "jobTitle",
      "workPhone",
      "homePhone",
      "mobilePhone",
      "faxNumber",
      "personalEmail",
      "businessEmail",
      "salutation",
      "id"
    ],
    "field_types": {
      "employerName": {
        "type": "string",
        "sample_value": "",
        "is_null": false,
        "is_empty": true
      },
      "birthdate": {
        "type": "string",
        "sample_value": "1967-09-08T00:00:00Z",
        "is_null": false,
        "is_empty": false
      },
      "borrowerType": {
        "type": "number",
        "sample_value": 1,
        "is_null": false,
        "is_empty": false
      },
      "primaryEmail": {
        "type": "string",
        "sample_value": "                                                  ",
        "is_null": false,
        "is_empty": false
      },
      "primaryPhone": {
        "type": "string",
        "sample_value": "",
        "is_null": false,
        "is_empty": true
      },
      "referral": {
        "type": "string",
        "sample_value": "Guillermo A Gonzalez realtor",
        "is_null": false,
        "is_empty": false
      },
      "firstName": {
        "type": "string",
        "sample_value": "BARBARA",
        "is_null": false,
        "is_empty": false
      },
      "lastName": {
        "type": "string",
        "sample_value": "HERRERA VEGA",
        "is_null": false,
        "is_empty": false
      },
      "ownerId": {
        "type": "string",
        "sample_value": "abejerano",
        "is_null": false,
        "is_empty": false
      },
      "accessLevel": {
        "type": "number",
        "sample_value": 0,
        "is_null": false,
        "is_empty": false
      },
      "currentMailingAddress": {
        "type": "object",
        "sample_value": {
          "city": "Opa Locka",
          "state": "FL",
          "street1": "2430 NW 140th St",
          "street2": "",
          "zip": "33054-4059",
          "unitType": ""
        },
        "is_null": false,
        "is_empty": false
      },
      "bizAddress": {
        "type": "object",
        "sample_value": {
          "city": "",
          "state": "",
          "street1": "",
          "street2": "",
          "zip": "",
          "unitType": ""
        },
        "is_null": false,
        "is_empty": false
      },
      "businessWebUrl": {
        "type": "object",
        "sample_value": null,
        "is_null": true,
        "is_empty": false
      },
      "jobTitle": {
        "type": "string",
        "sample_value": "",
        "is_null": false,
        "is_empty": true
      },
      "workPhone": {
        "type": "string",
        "sample_value": "************",
        "is_null": false,
        "is_empty": false
      },
      "homePhone": {
        "type": "string",
        "sample_value": "************",
        "is_null": false,
        "is_empty": false
      },
      "mobilePhone": {
        "type": "string",
        "sample_value": "",
        "is_null": false,
        "is_empty": true
      },
      "faxNumber": {
        "type": "string",
        "sample_value": "",
        "is_null": false,
        "is_empty": true
      },
      "personalEmail": {
        "type": "string",
        "sample_value": "<EMAIL>",
        "is_null": false,
        "is_empty": false
      },
      "businessEmail": {
        "type": "string",
        "sample_value": "",
        "is_null": false,
        "is_empty": true
      },
      "salutation": {
        "type": "string",
        "sample_value": "",
        "is_null": false,
        "is_empty": true
      },
      "id": {
        "type": "string",
        "sample_value": "a26af9f3-2d41-4ae1-9a41-366437ff8aaa",
        "is_null": false,
        "is_empty": false
      }
    }
ata