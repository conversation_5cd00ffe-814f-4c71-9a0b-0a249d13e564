const EncompassAuth = require('../utils/encompass-auth');

/**
 * Borrower Service
 * Handles all borrower-related operations with Encompass API
 */
class BorrowerService {
    constructor(config = {}) {
        this.encompassAuth = new EncompassAuth(config);
        this.baseUrl = config.baseUrl || process.env.ENCOMPASS_API_URL;
    }

    /**
     * Get total count of borrower contacts using simple API
     * @returns {Promise<number>} Total borrower count
     */
    async getTotalBorrowerCount() {
        try {
            console.log(`[${new Date().toISOString()}] 📊 Getting total borrower count using simple API...`);

            // Use the simple API without pagination
            const response = await this.encompassAuth.makeApiCall(
                `${this.baseUrl}/encompass/v1/borrowerContactSelector?start=1&limit=1`,
                {
                    method: 'POST',
                    data: {
                        fields: ["Contact.FirstName", "Contact.LastName"] // Minimal fields for count
                    }
                },
                'Getting total borrower count'
            );

            const totalCount = response.totalRecords || 0;
            console.log(`[${new Date().toISOString()}] ✅ Total borrower contacts: ${totalCount.toLocaleString()}`);

            return totalCount;

        } catch (error) {
            console.error(`[${new Date().toISOString()}] ❌ Error getting borrower count:`, error.response?.data || error.message);

            // If the API fails, return 0 for now
            console.log(`[${new Date().toISOString()}] ⚠️ Could not determine borrower count`);
            return 0;
        }
    }

    /**
     * Get borrower contacts with full data using simple API
     * @param {number} limit - Number of contacts to retrieve (default 100, max 10000)
     * @param {number} start - Starting index (default 1)
     * @returns {Promise<object>} Borrower contacts response with full data
     */
    async getBorrowerContactIds(limit = 100, start = 1) {
        try {
            const actualLimit = Math.min(limit, 10000); // API max limit
            const url = `${this.baseUrl}/encompass/v1/borrowerContactSelector?start=${start}&limit=${actualLimit}`;

            const response = await this.encompassAuth.makeApiCall(
                url,
                {
                    method: 'POST',
                    data: {
                        fields: [
                            "Contact.FirstName",
                            "Contact.LastName",
                            "Contact.PersonalEmail",
                            "Contact.HomePhone",
                            "Contact.MobilePhone",
                            "Contact.WorkPhone"
                        ]
                    }
                },
                `Getting ${actualLimit} borrower contacts starting from ${start}`
            );

            return {
                contacts: response.borrowerContacts || [],
                totalRecords: response.totalRecords || 0,
                currentStart: start,
                currentLimit: actualLimit,
                hasMore: (start + actualLimit - 1) < (response.totalRecords || 0)
            };

        } catch (error) {
            console.error(`[${new Date().toISOString()}] ❌ Error getting borrower contacts:`, error.response?.data || error.message);
            throw error;
        }
    }

    /**
     * Get all borrower contacts (up to 100 for demo)
     * @returns {Promise<Array>} Array of borrower contacts with full data
     */
    async getAllBorrowerContacts() {
        try {
            console.log(`[${new Date().toISOString()}] 📋 Getting all borrower contacts...`);

            const response = await this.getBorrowerContactIds(100, 1);

            console.log(`[${new Date().toISOString()}] ✅ Retrieved ${response.contacts.length} borrower contacts out of ${response.totalRecords} total`);

            return response.contacts;

        } catch (error) {
            console.error(`[${new Date().toISOString()}] ❌ Error getting all borrower contacts:`, error.response?.data || error.message);
            throw error;
        }
    }

    /**
     * Get detailed borrower contact information
     * @param {string} contactId - Borrower contact ID
     * @returns {Promise<object>} Borrower contact details
     */
    async getBorrowerContactDetails(contactId) {
        try {
            const response = await this.encompassAuth.makeApiCall(
                `${this.baseUrl}/encompass/v1/borrowerContacts/${contactId}`,
                { method: 'GET' },
                `Getting Encompass contact details for ${contactId}`
            );

            return response;

        } catch (error) {
            console.error(`[${new Date().toISOString()}] ❌ Error getting borrower contact details for ${contactId}:`, error.response?.data || error.message);
            throw error;
        }
    }

    /**
     * Transform Encompass borrower data to GoHighLevel format
     * @param {object} encompassContact - Encompass contact data
     * @returns {object} GoHighLevel formatted contact data
     */
    transformToGHLFormat(encompassContact) {
        const phones = [];
        if (encompassContact.homePhone) phones.push(encompassContact.homePhone);
        if (encompassContact.workPhone) phones.push(encompassContact.workPhone);
        if (encompassContact.mobilePhone) phones.push(encompassContact.mobilePhone);

        const primaryPhone = phones[0] || '';
        const address = encompassContact.currentMailingAddress;
        const fullAddress = address ? 
            `${address.street1 || ''} ${address.street2 || ''}, ${address.city || ''}, ${address.state || ''} ${address.zip || ''}`.trim() : '';

        return {
            // Standard GoHighLevel fields
            firstName: encompassContact.firstName || '',
            lastName: encompassContact.lastName || '',
            name: `${encompassContact.firstName || ''} ${encompassContact.lastName || ''}`.trim(),
            email: encompassContact.personalEmail || encompassContact.businessEmail || '',
            phone: primaryPhone,
            address1: address?.street1 || '',
            city: address?.city || '',
            state: address?.state || '',
            postalCode: address?.zip || '',
            country: 'US',
            source: 'Encompass API',
            
            // Custom fields for additional borrower data
            customFields: [
                { key: 'encompass_contact_id', field_value: encompassContact.id || '' },
                { key: 'date_of_birth', field_value: encompassContact.birthdate || '' },
                { key: 'home_phone', field_value: encompassContact.homePhone || '' },
                { key: 'work_phone', field_value: encompassContact.workPhone || '' },
                { key: 'mobile_phone', field_value: encompassContact.mobilePhone || '' },
                { key: 'business_email', field_value: encompassContact.businessEmail || '' },
                { key: 'personal_email', field_value: encompassContact.personalEmail || '' },
                { key: 'full_address', field_value: fullAddress },
                { key: 'realtor_name', field_value: encompassContact.referral || '' },
                { key: 'import_date', field_value: new Date().toISOString() },
                { key: 'borrower_type', field_value: 'Primary Borrower' },
                { key: 'employer_name', field_value: encompassContact.employerName || '' },
                { key: 'job_title', field_value: encompassContact.jobTitle || '' },
                
                // Placeholder fields for loan data (when API access is available)
                { key: 'interest_rate', field_value: '' },
                { key: 'closing_date', field_value: '' },
                { key: 'loan_originator', field_value: '' },
                { key: 'realtor_phone', field_value: '' },
                { key: 'realtor_email', field_value: '' },
                { key: 'property_address', field_value: '' },
                { key: 'loan_amount', field_value: '' },
                { key: 'loan_type', field_value: '' }
            ].filter(field => field.field_value !== ''), // Remove empty custom fields
            
            // Tags for organization and tracking
            tags: ['Encompass Import', 'Mortgage Lead', 'Borrower', 'Real Estate']
        };
    }

    /**
     * Extract comprehensive data from multiple sources
     * @param {object} borrowerContact - Borrower contact data
     * @param {object} loanData - Loan data (optional)
     * @param {array} loanAssociates - Loan associates data (optional)
     * @returns {object} Comprehensive borrower record
     */
    extractComprehensiveData(borrowerContact, loanData = null, loanAssociates = null) {
        const comprehensiveRecord = {
            // Borrower Information (from borrower contacts API)
            borrower: {
                id: borrowerContact.id,
                name: `${borrowerContact.firstName || ''} ${borrowerContact.lastName || ''}`.trim(),
                firstName: borrowerContact.firstName,
                lastName: borrowerContact.lastName,
                email: borrowerContact.personalEmail || borrowerContact.businessEmail,
                phones: {
                    home: borrowerContact.homePhone,
                    work: borrowerContact.workPhone,
                    mobile: borrowerContact.mobilePhone
                },
                dateOfBirth: borrowerContact.birthdate,
                address: borrowerContact.currentMailingAddress,
                employer: {
                    name: borrowerContact.employerName,
                    jobTitle: borrowerContact.jobTitle
                }
            },
            
            // Loan Information (from loan APIs when available)
            loan: {
                id: loanData?.loanNumber || null,
                guid: loanData?.guid || null,
                interestRate: null,
                closingDate: null,
                propertyAddress: null,
                loanOriginator: null,
                amount: null,
                type: null
            },
            
            // Realtor Information (basic from referral field, enhanced if business contact found)
            realtor: {
                name: borrowerContact.referral || null,
                phone: null,
                email: null,
                businessContactId: null
            },
            
            // Data source tracking
            dataSources: {
                borrowerContact: !!borrowerContact,
                loanData: !!loanData,
                loanAssociates: !!loanAssociates,
                realtorBusinessContact: false
            },

            // Metadata
            metadata: {
                extractedAt: new Date().toISOString(),
                source: 'Encompass API',
                version: '1.0'
            }
        };

        // Extract loan-specific data if available
        if (loanData) {
            // Try to extract interest rate from various possible field locations
            comprehensiveRecord.loan.interestRate = 
                loanData.fields?.['4'] || 
                loanData.interestRate || 
                loanData.noteRate || 
                null;
                
            // Try to extract closing date from various possible field locations
            comprehensiveRecord.loan.closingDate = 
                loanData.fields?.['763'] || 
                loanData.closingDate || 
                loanData.estimatedClosingDate || 
                null;
                
            // Try to extract property address
            comprehensiveRecord.loan.propertyAddress = 
                loanData.fields?.['11'] || 
                loanData.propertyAddress || 
                loanData.subjectPropertyAddress || 
                null;

            // Try to extract loan amount
            comprehensiveRecord.loan.amount = 
                loanData.fields?.['1109'] || 
                loanData.loanAmount || 
                loanData.requestedLoanAmount || 
                null;

            // Try to extract loan type
            comprehensiveRecord.loan.type = 
                loanData.fields?.['1172'] || 
                loanData.loanType || 
                loanData.loanPurpose || 
                null;
        }

        // Extract loan originator information from associates
        if (loanAssociates && Array.isArray(loanAssociates)) {
            const loanOriginator = loanAssociates.find(associate => 
                associate.roleType === 'LoanOfficer' || 
                associate.roleType === 'LoanOriginator' ||
                associate.role?.toLowerCase().includes('originator') ||
                associate.role?.toLowerCase().includes('officer')
            );
            
            if (loanOriginator) {
                comprehensiveRecord.loan.loanOriginator = {
                    name: loanOriginator.name || `${loanOriginator.firstName || ''} ${loanOriginator.lastName || ''}`.trim(),
                    email: loanOriginator.email,
                    phone: loanOriginator.phone,
                    role: loanOriginator.role || loanOriginator.roleType,
                    id: loanOriginator.id
                };
            }
        }

        return comprehensiveRecord;
    }
}

module.exports = BorrowerService;
