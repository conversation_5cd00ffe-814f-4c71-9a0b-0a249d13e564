const axios = require('axios');
require('dotenv').config();

// Configuration
const baseUrl = 'https://api.elliemae.com';
const username = process.env.ENCOMPASS_USERNAME;
const password = process.env.ENCOMPASS_PASSWORD;
const clientId = process.env.ENCOMPASS_CLIENT_ID;
const clientSecret = process.env.ENCOMPASS_CLIENT_SECRET;

console.log('🔍 Getting Total Borrower Contacts Count');
console.log('='.repeat(50));

async function getAccessToken() {
  try {
    console.log('🔐 Getting access token...');
    
    const tokenResponse = await axios.post(`${baseUrl}/oauth2/v1/token`, 
      `grant_type=password&username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`,
      {
        headers: {
          'Authorization': `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`,
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );
    
    console.log('✅ Access token obtained');
    return tokenResponse.data.access_token;
  } catch (error) {
    console.error('❌ Error getting access token:', error.response?.data || error.message);
    throw error;
  }
}

async function getTotalContactsCount(accessToken) {
  const headers = {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  };

  console.log('\n📊 Counting total borrower contacts...');

  let totalCount = 0;
  let currentStart = 1;
  let pageSize = 1000;
  let pageCount = 0;
  let hasMore = true;
  
  while (hasMore) {
    console.log(`📄 Checking page ${pageCount + 1} (start: ${currentStart})`);
    
    try {
      const response = await axios.post(
        `${baseUrl}/encompass/v1/borrowerContactSelector`,
        { start: currentStart, limit: pageSize },
        { headers }
      );
      
      let contactsInPage = 0;
      if (Array.isArray(response.data)) {
        contactsInPage = response.data.length;
      }
      
      console.log(`   📈 Found ${contactsInPage} contacts on page ${pageCount + 1}`);
      totalCount += contactsInPage;
      
      if (contactsInPage < pageSize) {
        console.log(`   🏁 Last page reached (${contactsInPage} < ${pageSize})`);
        hasMore = false;
      } else {
        currentStart += pageSize;
        pageCount++;
      }
      
      // Small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 500));
      
    } catch (error) {
      console.error(`❌ Error on page ${pageCount + 1}:`, error.response?.status, error.response?.data || error.message);
      hasMore = false;
    }
  }
  
  return {
    totalCount,
    pagesChecked: pageCount + 1,
    contactsPerPage: pageSize
  };
}

async function main() {
  try {
    const accessToken = await getAccessToken();
    const result = await getTotalContactsCount(accessToken);
    
    console.log('\n📊 TOTAL CONTACTS COUNT RESULT');
    console.log('='.repeat(40));
    console.log(`✅ Total Borrower Contacts: ${result.totalCount.toLocaleString()}`);
    console.log(`📄 Pages checked: ${result.pagesChecked}`);
    console.log(`📋 Contacts per page: ${result.contactsPerPage}`);
    console.log(`🔄 Pagination working: ${result.pagesChecked > 1 ? 'YES' : 'NO'}`);
    
    // Save count to file
    const countData = {
      timestamp: new Date().toISOString(),
      total_borrower_contacts: result.totalCount,
      pages_checked: result.pagesChecked,
      contacts_per_page: result.contactsPerPage,
      pagination_working: result.pagesChecked > 1
    };
    
    const fs = require('fs');
    const filename = `data/borrower-contacts-count-${Date.now()}.json`;
    fs.writeFileSync(filename, JSON.stringify(countData, null, 2));
    console.log(`💾 Count saved to: ${filename}`);
    
  } catch (error) {
    console.error('💥 Fatal error:', error.message);
    process.exit(1);
  }
}

main();
