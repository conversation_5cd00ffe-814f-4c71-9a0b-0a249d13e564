const express = require('express');
const axios = require('axios');
const crypto = require('crypto');
const sqlite3 = require('sqlite3').verbose();
const fs = require('fs');
require('dotenv').config();

// Configuration
const app = express();
const PORT = process.env.WEBHOOK_PORT || 3000;

// Encompass Configuration
const baseUrl = 'https://api.elliemae.com';
const username = process.env.ENCOMPASS_USERNAME;
const password = process.env.ENCOMPASS_PASSWORD;
const clientId = process.env.ENCOMPASS_CLIENT_ID;
const clientSecret = process.env.ENCOMPASS_CLIENT_SECRET;
const instanceId = process.env.ENCOMPASS_INSTANCE_ID;

// GoHighLevel Configuration
const ghlApiKey = process.env.GHL_API_KEY;
const ghlLocationId = process.env.GHL_LOCATION_ID;

// Webhook Configuration
const webhookSigningKey = process.env.ENCOMPASS_WEBHOOK_SIGNING_KEY;
const webhookCallbackUrl = process.env.WEBHOOK_CALLBACK_URL || `http://localhost:${PORT}/webhook/encompass`;

// Middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.raw({ type: 'application/json', limit: '10mb' }));

// Database setup
const db = new sqlite3.Database('./webhook_logs.db');

// Initialize database
db.serialize(() => {
    db.run(`CREATE TABLE IF NOT EXISTS webhook_events (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        event_id TEXT UNIQUE,
        event_type TEXT,
        resource_type TEXT,
        resource_id TEXT,
        user_id TEXT,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        payload TEXT,
        processed BOOLEAN DEFAULT FALSE,
        ghl_contact_id TEXT,
        error_message TEXT
    )`);
    
    db.run(`CREATE TABLE IF NOT EXISTS encompass_tokens (
        id INTEGER PRIMARY KEY,
        access_token TEXT,
        expires_at DATETIME
    )`);
});

// Authentication functions
async function getEncompassToken() {
    try {
        const response = await axios.post(`${baseUrl}/oauth2/v1/token`, {
            grant_type: 'password',
            username: username,
            password: password,
            client_id: clientId,
            client_secret: clientSecret
        }, {
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const token = response.data.access_token;
        const expiresIn = response.data.expires_in;
        const expiresAt = new Date(Date.now() + (expiresIn * 1000));

        // Store token in database
        db.run('INSERT OR REPLACE INTO encompass_tokens (id, access_token, expires_at) VALUES (1, ?, ?)', 
               [token, expiresAt.toISOString()]);

        return token;
    } catch (error) {
        console.error('❌ Error getting Encompass token:', error.response?.data || error.message);
        throw error;
    }
}

async function getValidToken() {
    return new Promise((resolve, reject) => {
        db.get('SELECT access_token, expires_at FROM encompass_tokens WHERE id = 1', (err, row) => {
            if (err) {
                reject(err);
                return;
            }

            if (row && new Date(row.expires_at) > new Date()) {
                resolve(row.access_token);
            } else {
                getEncompassToken().then(resolve).catch(reject);
            }
        });
    });
}

// GoHighLevel functions
async function createGHLContact(borrowerData) {
    try {
        const contactData = {
            firstName: borrowerData.firstName || '',
            lastName: borrowerData.lastName || '',
            email: borrowerData.personalEmail || borrowerData.workEmail || '',
            phone: borrowerData.homePhone || borrowerData.workPhone || borrowerData.mobilePhone || '',
            address1: borrowerData.currentMailingAddress?.addressLineText || '',
            city: borrowerData.currentMailingAddress?.cityName || '',
            state: borrowerData.currentMailingAddress?.stateCode || '',
            postalCode: borrowerData.currentMailingAddress?.postalCode || '',
            source: 'Encompass Integration',
            tags: ['Encompass Lead', 'Borrower Contact'],
            customFields: {
                encompass_id: borrowerData.id,
                encompass_owner_id: borrowerData.ownerId,
                birth_date: borrowerData.birthdate,
                referral_source: borrowerData.referral
            }
        };

        const response = await axios.post(
            `https://services.leadconnectorhq.com/contacts/`,
            contactData,
            {
                headers: {
                    'Authorization': `Bearer ${ghlApiKey}`,
                    'Content-Type': 'application/json',
                    'Version': '2021-07-28'
                }
            }
        );

        console.log('✅ Created GHL contact:', response.data.contact.id);
        return response.data.contact.id;
    } catch (error) {
        console.error('❌ Error creating GHL contact:', error.response?.data || error.message);
        throw error;
    }
}

// Webhook signature verification
function verifyWebhookSignature(payload, signature, signingKey) {
    if (!signingKey) {
        console.warn('⚠️ No webhook signing key configured - skipping signature verification');
        return true;
    }

    const expectedSignature = crypto
        .createHmac('sha256', signingKey)
        .update(payload)
        .digest('hex');

    return crypto.timingSafeEqual(
        Buffer.from(signature, 'hex'),
        Buffer.from(expectedSignature, 'hex')
    );
}

// Webhook endpoint
app.post('/webhook/encompass', async (req, res) => {
    try {
        const signature = req.headers['x-encompass-signature'];
        const payload = JSON.stringify(req.body);

        // Verify webhook signature
        if (!verifyWebhookSignature(payload, signature, webhookSigningKey)) {
            console.error('❌ Invalid webhook signature');
            return res.status(401).json({ error: 'Invalid signature' });
        }

        const event = req.body;
        console.log('📨 Received webhook event:', {
            eventId: event.eventId,
            eventType: event.eventType,
            resourceType: event.meta?.resourceType,
            resourceId: event.meta?.resourceId
        });

        // Store event in database
        db.run(`INSERT OR IGNORE INTO webhook_events 
                (event_id, event_type, resource_type, resource_id, user_id, payload) 
                VALUES (?, ?, ?, ?, ?, ?)`,
               [event.eventId, event.eventType, event.meta?.resourceType, 
                event.meta?.resourceId, event.meta?.userId, payload]);

        // Process the event
        await processWebhookEvent(event);

        res.status(200).json({ status: 'success', eventId: event.eventId });
    } catch (error) {
        console.error('❌ Webhook processing error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Process webhook events
async function processWebhookEvent(event) {
    try {
        // Check if this is a relevant event for borrower contacts
        if (event.meta?.resourceType === 'ExternalUsers' && event.eventType === 'create') {
            console.log('🎯 Processing new external user (potential borrower) creation');
            await handleNewBorrowerContact(event);
        } else if (event.meta?.resourceType === 'InternalUsers' && event.eventType === 'create') {
            console.log('🎯 Processing new internal user creation');
            await handleNewBorrowerContact(event);
        } else {
            console.log('ℹ️ Event not relevant for borrower contact processing:', event.meta?.resourceType, event.eventType);
        }
    } catch (error) {
        console.error('❌ Error processing webhook event:', error);
        
        // Update database with error
        db.run('UPDATE webhook_events SET error_message = ? WHERE event_id = ?',
               [error.message, event.eventId]);
    }
}

// Handle new borrower contact creation
async function handleNewBorrowerContact(event) {
    try {
        const userId = event.meta?.payload?.entities?.[0]?.id;
        if (!userId) {
            console.log('⚠️ No user ID found in webhook payload');
            return;
        }

        console.log('🔍 Fetching borrower contact details for user:', userId);
        
        // Get fresh token
        const token = await getValidToken();
        
        // Try to fetch as borrower contact first
        let borrowerData = null;
        try {
            const response = await axios.get(
                `${baseUrl}/encompass/v1/borrowerContacts/${userId}`,
                {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );
            borrowerData = response.data;
            console.log('✅ Found borrower contact data');
        } catch (error) {
            if (error.response?.status === 404) {
                console.log('ℹ️ User not found as borrower contact, might be internal user');
                return;
            }
            throw error;
        }

        if (borrowerData) {
            // Create contact in GoHighLevel
            const ghlContactId = await createGHLContact(borrowerData);
            
            // Update database
            db.run('UPDATE webhook_events SET processed = TRUE, ghl_contact_id = ? WHERE event_id = ?',
                   [ghlContactId, event.eventId]);
            
            console.log('🎉 Successfully processed new borrower contact:', {
                encompassId: borrowerData.id,
                ghlContactId: ghlContactId,
                name: `${borrowerData.firstName} ${borrowerData.lastName}`
            });
        }
    } catch (error) {
        console.error('❌ Error handling new borrower contact:', error);
        throw error;
    }
}

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ 
        status: 'healthy', 
        timestamp: new Date().toISOString(),
        webhookUrl: webhookCallbackUrl
    });
});

// Status endpoint
app.get('/status', (req, res) => {
    db.all(`SELECT 
                COUNT(*) as total_events,
                COUNT(CASE WHEN processed = TRUE THEN 1 END) as processed_events,
                COUNT(CASE WHEN error_message IS NOT NULL THEN 1 END) as error_events
            FROM webhook_events`, (err, rows) => {
        if (err) {
            return res.status(500).json({ error: err.message });
        }
        
        res.json({
            webhook_stats: rows[0],
            last_24h_events: 'Query not implemented yet'
        });
    });
});

// Start server
app.listen(PORT, () => {
    console.log('🚀 Encompass Webhook Integration Server Started');
    console.log('='.repeat(50));
    console.log(`📡 Server running on port: ${PORT}`);
    console.log(`🔗 Webhook URL: ${webhookCallbackUrl}`);
    console.log(`🏥 Health check: http://localhost:${PORT}/health`);
    console.log(`📊 Status: http://localhost:${PORT}/status`);
    console.log('='.repeat(50));
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down webhook server...');
    db.close();
    process.exit(0);
});
