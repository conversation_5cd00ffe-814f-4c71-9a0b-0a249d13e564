const axios = require('axios');

/**
 * Encompass Authentication Utility
 * Handles OAuth2 authentication for Encompass API
 */
class EncompassAuth {
    constructor(config) {
        this.baseUrl = config.baseUrl || process.env.ENCOMPASS_API_URL;
        this.clientId = config.clientId || process.env.ENCOMPASS_CLIENT_ID;
        this.clientSecret = config.clientSecret || process.env.ENCOMPASS_CLIENT_SECRET;
        this.username = config.username || process.env.ENCOMPASS_USERNAME;
        this.password = config.password || process.env.ENCOMPASS_PASSWORD;
        this.accessToken = null;
        this.tokenExpiry = null;
    }

    /**
     * Get access token for Encompass API
     * @returns {Promise<string>} Access token
     */
    async getAccessToken() {
        // Check if we have a valid token
        if (this.accessToken && this.tokenExpiry && Date.now() < this.tokenExpiry) {
            return this.accessToken;
        }

        try {
            console.log(`[${new Date().toISOString()}] Getting Encompass access token...`);

            const tokenResponse = await axios.post(
                `${this.baseUrl}/oauth2/v1/token`,
                `grant_type=password&username=${encodeURIComponent(this.username)}&password=${encodeURIComponent(this.password)}`,
                {
                    headers: {
                        'Authorization': `Basic ${Buffer.from(`${this.clientId}:${this.clientSecret}`).toString('base64')}`,
                        'Content-Type': 'application/x-www-form-urlencoded'
                    }
                }
            );

            this.accessToken = tokenResponse.data.access_token;
            // Set expiry to 50 minutes (tokens typically last 1 hour)
            this.tokenExpiry = Date.now() + (50 * 60 * 1000);

            console.log(`[${new Date().toISOString()}] ✅ Encompass access token obtained successfully`);
            return this.accessToken;

        } catch (error) {
            console.error(`[${new Date().toISOString()}] ❌ Error getting Encompass access token:`, error.response?.data || error.message);
            throw new Error(`Failed to get Encompass access token: ${error.response?.data?.error_description || error.message}`);
        }
    }

    /**
     * Make authenticated API call to Encompass
     * @param {string} url - API endpoint URL
     * @param {object} options - Axios request options
     * @param {string} description - Description for logging
     * @returns {Promise<any>} API response data
     */
    async makeApiCall(url, options = {}, description = 'API call') {
        const token = await this.getAccessToken();
        
        const config = {
            ...options,
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
                ...options.headers
            }
        };

        try {
            console.log(`[${new Date().toISOString()}] 🌐 ${description}`);
            const response = await axios(url, config);
            
            // Rate limiting
            await new Promise(resolve => setTimeout(resolve, 200));
            
            return response.data;
        } catch (error) {
            console.error(`[${new Date().toISOString()}] ❌ ${description} failed:`, error.response?.data || error.message);
            throw error;
        }
    }

    /**
     * Refresh access token
     */
    async refreshToken() {
        this.accessToken = null;
        this.tokenExpiry = null;
        return await this.getAccessToken();
    }
}

module.exports = EncompassAuth;
