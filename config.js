require('dotenv').config();

const config = {
  encompass: {
    apiUrl: process.env.ENCOMPASS_API_URL || 'https://api.elliemae.com',
    clientId: process.env.ENCOMPASS_CLIENT_ID,
    clientSecret: process.env.ENCOMPASS_CLIENT_SECRET,
    username: process.env.ENCOMPASS_USERNAME,
    password: process.env.ENCOMPASS_PASSWORD,
    instanceId: process.env.ENCOMPASS_INSTANCE_ID
  },
  gohighlevel: {
    apiUrl: process.env.GOHIGHLEVEL_API_URL || 'https://rest.gohighlevel.com/v1',
    apiKey: process.env.GOHIGHLEVEL_API_KEY,
    locationId: process.env.GOHIGHLEVEL_LOCATION_ID
  },
  sync: {
    intervalMinutes: parseInt(process.env.SYNC_INTERVAL_MINUTES) || 5,
    logLevel: process.env.LOG_LEVEL || 'info'
  }
};

// Validate required configuration
function validateConfig() {
  const required = [
    'encompass.clientId',
    'encompass.clientSecret',
    'encompass.username',
    'encompass.password',
    'encompass.instanceId',
    'gohighlevel.apiKey',
    'gohighlevel.locationId'
  ];

  const missing = required.filter(key => {
    const value = key.split('.').reduce((obj, k) => obj && obj[k], config);
    return !value;
  });

  if (missing.length > 0) {
    throw new Error(`Missing required configuration: ${missing.join(', ')}`);
  }
}

module.exports = { config, validateConfig };
