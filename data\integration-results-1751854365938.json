{"summary": {"totalProcessed": 3, "created": 3, "updated": 0, "errors": 6, "apiCalls": 13, "elapsedTime": 10.52}, "processedContacts": [{"encompassId": "a26af9f3-2d41-4ae1-9a41-366437ff8aaa", "ghlContactId": "ieJuapqR19A22spHPIij", "action": "created", "name": "BARBARA HERRERA VEGA", "email": "<EMAIL>", "phone": "************"}, {"encompassId": "7daa9d4a-5411-4a47-8650-0bf2946a5a4d", "ghlContactId": "F7HQZG4VBTV2tZlruFZp", "action": "created", "name": "ORLANDO GIL", "email": "<EMAIL>", "phone": "************"}, {"encompassId": "95671b80-e118-4aa8-aaf9-cd55db29dbff", "ghlContactId": "LN2gHsOmq9inhCkQkLdr", "action": "created", "name": "ARIANA RODRIGUEZ", "email": "<EMAIL>", "phone": "************"}], "errors": [{"description": "Searching for existing contact by email: <EMAIL>", "error": {"message": "Cannot POST /contacts/search/duplicate", "error": "Not Found", "statusCode": 404}}, {"description": "Searching for existing contact by phone: ************", "error": {"message": "Cannot POST /contacts/search/duplicate", "error": "Not Found", "statusCode": 404}}, {"description": "Searching for existing contact by email: <EMAIL>", "error": {"message": "Cannot POST /contacts/search/duplicate", "error": "Not Found", "statusCode": 404}}, {"description": "Searching for existing contact by phone: ************", "error": {"message": "Cannot POST /contacts/search/duplicate", "error": "Not Found", "statusCode": 404}}, {"description": "Searching for existing contact by email: a<PERSON><PERSON>drig<PERSON><EMAIL>", "error": {"message": "Cannot POST /contacts/search/duplicate", "error": "Not Found", "statusCode": 404}}, {"description": "Searching for existing contact by phone: ************", "error": {"message": "Cannot POST /contacts/search/duplicate", "error": "Not Found", "statusCode": 404}}]}